<?php
/**
 * Bamboo Web Application - Update Wallet Address API
 * Company: Notepadsly
 * Version: 1.0
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$user_id = (int)($_POST['user_id'] ?? 0);
$usdt_wallet_address = sanitizeInput($_POST['usdt_wallet_address'] ?? '');
$exchange_name = sanitizeInput($_POST['exchange_name'] ?? '');

if ($user_id <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid user ID.'], 400);
}

// Allow wallet address and exchange name to be empty (user can clear them)
// But if they are not empty, they should be updated.
$update_data = [];
if (!empty($usdt_wallet_address)) {
    $update_data['usdt_wallet_address'] = $usdt_wallet_address;
} else {
    $update_data['usdt_wallet_address'] = null; // Set to NULL if empty string provided
}

if (!empty($exchange_name)) {
    $update_data['exchange_name'] = $exchange_name;
} else {
    $update_data['exchange_name'] = null; // Set to NULL if empty string provided
}


if (empty($update_data)) {
    jsonResponse(['success' => false, 'message' => 'No wallet address or exchange name provided for update.'], 400);
}

try {
    beginTransaction();

    // Update wallet address and exchange name
    $update_success = updateRecord('users', $update_data, 'id = ?', [$user_id]);

    if (!$update_success) {
        rollbackTransaction();
        $error = getLastError();
        if (isset($error['message'])) {
            jsonResponse(['success' => false, 'message' => $error['message']], 500);
        } else {
            jsonResponse(['success' => false, 'message' => 'Failed to update wallet information.'], 500);
        }
    }

    commitTransaction();
    jsonResponse(['success' => true, 'message' => 'Wallet information updated successfully!']);

} catch (Exception $e) {
    rollbackTransaction();
    logError('Error updating wallet information: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}
?>
