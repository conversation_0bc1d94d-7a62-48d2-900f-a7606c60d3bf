/**
 * Bamboo Web Application - Admin Dashboard Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== DASHBOARD SPECIFIC STYLES ===== */

/* Statistics Cards */
.stat-card {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    background: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.04);
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
    border-color: rgba(0, 0, 0, 0.08);
}

.stat-card::before {
    display: none !important;
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.6));
    border-radius: 1rem 1rem 0 0;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover::after {
    opacity: 1;
}

.stat-icon {
    width: 64px;
    height: 64px;
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.3);
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.05);
    box-shadow: 0 0.5rem 1.5rem rgba(var(--admin-primary-rgb), 0.4);
}

.stat-card-primary .stat-icon {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
    box-shadow: 0 0.25rem 0.75rem rgba(79, 70, 229, 0.3);
}

.stat-card-success .stat-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 0.25rem 0.75rem rgba(16, 185, 129, 0.3);
}

.stat-card-info .stat-icon {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    box-shadow: 0 0.25rem 0.75rem rgba(6, 182, 212, 0.3);
}

.stat-card-warning .stat-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    box-shadow: 0 0.25rem 0.75rem rgba(245, 158, 11, 0.3);
}

.stat-card-danger .stat-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 0.25rem 0.75rem rgba(239, 68, 68, 0.3);
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--admin-text-muted);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

/* Dashboard Cards */
.card {
    border: 1px solid rgba(0, 0, 0, 0.04);
    border-radius: 1rem;
    background: #ffffff;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
    border-color: rgba(0, 0, 0, 0.08);
}

.card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    border-radius: 0 !important;
    padding: 1.25rem 1.5rem;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.card-title {
    color: var(--admin-text-dark);
    font-weight: 600;
    margin-bottom: 0;
    font-size: 1.1rem;
}

.card-body {
    padding: 1.5rem;
}

/* List Groups */
.list-group-item {
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
    padding: 1.25rem 1.5rem;
    transition: all 0.3s ease;
    background: transparent;
}

.list-group-item:hover {
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    transform: translateX(4px);
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.list-group-item:last-child {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* Quick Actions */
.btn {
    border-radius: 0.75rem;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-outline-primary {
    border-color: rgba(79, 70, 229, 0.2);
    color: #4f46e5;
    background: rgba(79, 70, 229, 0.05);
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
    border-color: #4f46e5;
    color: white;
    box-shadow: 0 0.5rem 1.5rem rgba(79, 70, 229, 0.3);
}

.btn-outline-success {
    border-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
    background: rgba(16, 185, 129, 0.05);
}

.btn-outline-success:hover {
    background: linear-gradient(135deg, #10b981, #059669);
    border-color: #10b981;
    color: white;
    box-shadow: 0 0.5rem 1.5rem rgba(16, 185, 129, 0.3);
}

.btn-outline-info {
    border-color: rgba(6, 182, 212, 0.2);
    color: #06b6d4;
    background: rgba(6, 182, 212, 0.05);
}

.btn-outline-info:hover {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    border-color: #06b6d4;
    color: white;
    box-shadow: 0 0.5rem 1.5rem rgba(6, 182, 212, 0.3);
}

.btn-outline-warning {
    border-color: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.05);
}

.btn-outline-warning:hover {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    border-color: #f59e0b;
    color: white;
    box-shadow: 0 0.5rem 1.5rem rgba(245, 158, 11, 0.3);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.5rem 0.875rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    letter-spacing: 0.025em;
}

.badge.bg-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(16, 185, 129, 0.3);
}

.badge.bg-primary {
    background: linear-gradient(135deg, #4f46e5, #3730a3) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(79, 70, 229, 0.3);
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(245, 158, 11, 0.3);
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(239, 68, 68, 0.3);
}

.badge.bg-info {
    background: linear-gradient(135deg, #06b6d4, #0891b2) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(6, 182, 212, 0.3);
}

/* Page Header */
.admin-content h1 {
    color: var(--admin-text-dark);
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-icon {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .list-group-item {
        padding: 0.75rem;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart Containers */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* Dashboard Animations */
.stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--admin-text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Status Indicators */
.status-online {
    color: #28a745;
}

.status-offline {
    color: #dc3545;
}

.status-warning {
    color: #ffc107;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Enhanced Dashboard Layout */
.admin-content {
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    min-height: calc(100vh - var(--admin-topbar-height));
    padding: 2rem 0;
}

.container-fluid {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Page Header Enhancements */
.admin-content h1 {
    color: var(--admin-text-dark);
    font-weight: 700;
    font-size: 2.25rem;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--admin-text-dark), rgba(44, 62, 80, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.admin-content .text-muted {
    font-size: 1.1rem;
    color: #6b7280;
    font-weight: 400;
}

/* Status Badge Enhancements */
.badge.bg-success {
    animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Enhanced Typography */
.admin-content h1, .admin-content h2, .admin-content h3 {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
}

.admin-content p {
    line-height: 1.7;
    color: #6b7280;
}

/* Enhanced Spacing System */
.mb-section {
    margin-bottom: 3rem;
}

.mb-card {
    margin-bottom: 2rem;
}

/* Enhanced Grid System */
.dashboard-grid-responsive {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

@media (min-width: 768px) {
    .dashboard-grid-responsive {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    }
}

@media (min-width: 1200px) {
    .dashboard-grid-responsive {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

/* Enhanced Visual Hierarchy */
.section-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.04);
}

.section-header h2 {
    font-size: 1.75rem;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
}

.section-header p {
    font-size: 1rem;
    color: var(--admin-text-muted);
    margin-bottom: 0;
}

/* Enhanced Card Content */
.card-content-enhanced {
    padding: 2rem;
}

.card-content-enhanced h5 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 1rem;
}

.card-content-enhanced p {
    font-size: 0.875rem;
    line-height: 1.6;
    color: var(--admin-text-muted);
}

/* Enhanced Interactive Elements */
.interactive-element {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.interactive-element:hover {
    transform: translateY(-2px);
}

/* Enhanced Focus States */
.enhanced-focus:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--admin-primary-rgb), 0.2);
    border-color: var(--admin-primary);
}

/* Enhanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced Micro-interactions */
.micro-bounce {
    animation: micro-bounce 0.6s ease-out;
}

@keyframes micro-bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
    40%, 43% { transform: translate3d(0, -8px, 0); }
    70% { transform: translate3d(0, -4px, 0); }
    90% { transform: translate3d(0, -2px, 0); }
}

/* Enhanced Accessibility */
.sr-only-enhanced {
    position: absolute !important;
    width: 1px !important;
    height: 1px !important;
    padding: 0 !important;
    margin: -1px !important;
    overflow: hidden !important;
    clip: rect(0, 0, 0, 0) !important;
    white-space: nowrap !important;
    border: 0 !important;
}

/* Enhanced Print Styles */
@media print {
    .admin-sidebar,
    .admin-topbar,
    .btn,
    .badge {
        display: none !important;
    }

    .admin-main {
        margin-left: 0 !important;
    }

    .card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .stat-card {
        margin-bottom: 1rem !important;
    }
}

/* Custom Scrollbar for Dashboard */
.dashboard-scroll::-webkit-scrollbar {
    width: 6px;
}

.dashboard-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dashboard-scroll::-webkit-scrollbar-thumb {
    background: var(--admin-primary);
    border-radius: 3px;
}

.dashboard-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(var(--admin-primary-rgb), 0.8);
}