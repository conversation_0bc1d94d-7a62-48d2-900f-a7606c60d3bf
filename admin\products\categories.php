<?php
/**
 * Bamboo Web Application - Product Categories
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        $category_id = (int)($_POST['category_id'] ?? 0);
        $name = sanitizeInput($_POST['name'] ?? '');
        $description = sanitizeInput($_POST['description'] ?? '');

        if ($action === 'add' && !empty($name)) {
            insertRecord('product_categories', ['name' => $name, 'description' => $description]);
            showSuccess('Category added successfully!');
        } elseif ($action === 'edit' && $category_id > 0 && !empty($name)) {
            updateRecord('product_categories', ['name' => $name, 'description' => $description], 'id = ?', [$category_id]);
            showSuccess('Category updated successfully!');
        } elseif ($action === 'delete' && $category_id > 0) {
            deleteRecord('product_categories', 'id = ?', [$category_id]);
            showSuccess('Category deleted successfully!');
        }
        redirect('admin/products/categories.php');
        exit();
    }
}

$categories = fetchAll('SELECT * FROM product_categories ORDER BY name ASC');
$page_title = 'Product Categories';
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Product Categories</h1>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Add New Category</h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="POST">
                                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                    <input type="hidden" name="action" value="add">
                                    <div class="mb-3">
                                        <label for="name" class="form-label">Category Name</label>
                                        <input type="text" class="form-control" id="name" name="name" required>
                                    </div>
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                    </div>
                                    <button type="submit" class="btn btn-primary">Add Category</button>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">All Categories</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Description</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($categories)): foreach ($categories as $category): ?>
                                                <tr>
                                                    <td><?php echo htmlspecialchars($category['name']); ?></td>
                                                    <td><?php echo htmlspecialchars($category['description']); ?></td>
                                                    <td>
                                                        <form action="" method="POST" class="d-inline">
                                                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                                                            <input type="hidden" name="action" value="delete">
                                                            <input type="hidden" name="category_id" value="<?php echo $category['id']; ?>">
                                                            <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure?')">Delete</button>
                                                        </form>
                                                    </td>
                                                </tr>
                                            <?php endforeach; else: ?>
                                                <tr><td colspan="3" class="text-center text-muted">No categories found.</td></tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
