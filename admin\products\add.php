<?php
/**
 * Bamboo Web Application - Add Product
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        $name = sanitizeInput($_POST['name'] ?? '');
        $price = (float)($_POST['price'] ?? 0);
        $vip_level_id = (int)($_POST['vip_level_id'] ?? 0);
        $category_id = (int)($_POST['category_id'] ?? 0);
        $status = sanitizeInput($_POST['status'] ?? 'active');
        
        // Validation
        $errors = [];
        if (empty($name)) {
            $errors[] = 'Product name is required';
        }
        if ($price <= 0) {
            $errors[] = 'Price must be greater than 0';
        }
        
        // Handle file upload
        $image_url = '';
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_result = handleFileUpload($_FILES['image'], 'products/', ['jpg', 'jpeg', 'png', 'gif']);
            if ($upload_result['success']) {
                $image_url = $upload_result['file_url'];
            } else {
                $errors[] = 'Image upload failed: ' . $upload_result['message'];
            }
        }
        
        if (empty($errors)) {
            try {
                $product_data = [
                    'name' => $name,
                    'price' => $price,
                    'min_vip_level' => $vip_level_id,
                    'category_id' => $category_id,
                    'image_url' => $image_url,
                    'status' => $status,
                    'created_at' => date('Y-m-d H:i:s')
                ];
                
                if (insertRecord('products', $product_data)) {
                    showSuccess('Product added successfully!');
                    redirect('admin/products/');
                } else {
                    showError('Failed to add product. Please try again.');
                }
            } catch (Exception $e) {
                showError('Error adding product: ' . $e->getMessage());
            }
        }
    }
}

// Get VIP levels for dropdown
$vip_levels = fetchAll("SELECT id, name FROM vip_levels ORDER BY level ASC");

// Page configuration
$page_title = 'Add New Product';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css'
];

// Include admin header
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Add New Product</h1>
                    <a href="index.php" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to Products</a>
                </div>
                <div class="card">
                    <div class="card-body">
                        <form action="" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-3">
                                <label for="name" class="form-label">Product Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            <div class="mb-3">
                                <label for="price" class="form-label">Product Amount</label>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" required>
                            </div>
                            <div class="mb-3">
                                <label for="category_id" class="form-label">Category</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">Select Category</option>
                                    <?php 
                                    $categories = fetchAll('SELECT id, name FROM product_categories ORDER BY name ASC');
                                    foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"><?php echo htmlspecialchars($category['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="vip_level_id" class="form-label">Product Level (Required VIP Level)</label>
                                <select class="form-select" id="vip_level_id" name="vip_level_id">
                                    <option value="0">Any Level</option>
                                    <?php foreach ($vip_levels as $level): ?>
                                        <option value="<?php echo $level['id']; ?>"><?php echo htmlspecialchars($level['name']); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label for="image" class="form-label">Product Image</label>
                                <input type="file" class="form-control" id="image" name="image">
                            </div>
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" name="status">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js'
];
include '../includes/admin_footer_scripts.php';
?>