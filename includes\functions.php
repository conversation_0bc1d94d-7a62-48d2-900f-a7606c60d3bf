<?php
/**
 * Bamboo Web Application - Common Functions
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Include database functions
require_once 'database.php';

/**
 * Security Functions
 */

function getStatusBadgeClass($status) {
    switch ($status) {
        case 'pending':
            return 'warning';
        case 'active':
        case 'completed':
            return 'success';
        case 'suspended':
        case 'processing':
            return 'info';
        case 'banned':
        case 'failed':
        case 'cancelled':
            return 'danger';
        default:
            return 'secondary';
    }
}

// --- CSRF Protection ---
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time']) || 
        (time() - $_SESSION['csrf_token_time']) > CSRF_TOKEN_EXPIRE) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

// Verify CSRF token
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && 
           isset($_SESSION['csrf_token_time']) &&
           (time() - $_SESSION['csrf_token_time']) <= CSRF_TOKEN_EXPIRE &&
           hash_equals($_SESSION['csrf_token'], $token);
}

// Sanitize input
function sanitizeInput($input) {
    if (is_array($input)) {
        return array_map('sanitizeInput', $input);
    }
    // Ensure input is a string before trimming
    return htmlspecialchars(trim((string)$input), ENT_QUOTES, 'UTF-8');
}

// Validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Validate phone number (basic)
function isValidPhone($phone) {
    return preg_match('/^[\+]?[1-9][\d]{0,15}$/', $phone);
}

// Generate secure password hash
function hashPassword($password) {
    return password_hash($password, PASSWORD_ARGON2ID);
}

// Verify password
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Session Management
 */

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && isset($_SESSION['user_type']);
}

// Check if admin is logged in
function isAdminLoggedIn() {
    return isset($_SESSION['admin_id']) && $_SESSION['user_type'] === 'admin';
}

// Get current user ID
function getCurrentUserId() {
    return $_SESSION['user_id'] ?? null;
}

// Get current admin ID
function getCurrentAdminId() {
    return $_SESSION['admin_id'] ?? null;
}

// Logout user
function logout() {
    session_unset();
    session_destroy();
    session_start();
}

// Check session timeout
function checkSessionTimeout() {
    if (isset($_SESSION['last_activity']) && 
        (time() - $_SESSION['last_activity']) > SESSION_TIMEOUT) {
        logout();
        return false;
    }
    $_SESSION['last_activity'] = time();
    return true;
}

/**
 * URL and Routing Functions
 */

// Get base URL
function getBaseUrl() {
    return BASE_URL;
}

// Get assets URL
function getAssetsUrl() {
    return ASSETS_URL;
}

// Redirect function
function redirect($url, $permanent = false) {
    if (!headers_sent()) {
        if ($permanent) {
            header('HTTP/1.1 301 Moved Permanently');
        }
        
        // Handle relative URLs
        if (strpos($url, 'http') !== 0) {
            $url = BASE_URL . ltrim($url, '/');
        }
        
        header('Location: ' . $url);
        exit;
    }
}

// Get current URL
function getCurrentUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
    return $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * Template and Display Functions
 */

// Include header
function includeHeader($title = '', $additional_css = []) {
    $page_title = !empty($title) ? $title . ' - ' . APP_NAME : APP_NAME;
    include __DIR__ . '/header.php';
}

// Include footer
function includeFooter($additional_js = []) {
    include __DIR__ . '/footer.php';
}

// Display success message
function showSuccess($message) {
    $_SESSION['success_message'] = $message;
}

// Display error message
function showError($message) {
    $_SESSION['error_message'] = $message;
}

// Get and clear flash messages
function getFlashMessages() {
    $messages = [];
    
    if (isset($_SESSION['success_message'])) {
        $messages['success'] = $_SESSION['success_message'];
        unset($_SESSION['success_message']);
    }
    
    if (isset($_SESSION['error_message'])) {
        $messages['error'] = $_SESSION['error_message'];
        unset($_SESSION['error_message']);
    }
    
    return $messages;
}

/**
 * Utility Functions
 */

// Format currency
function formatCurrency($amount, $currency = null) {
    // Handle null or non-numeric values
    if ($amount === null || $amount === '' || !is_numeric($amount)) {
        $amount = 0;
    }

    if ($currency === null) {
        $currency = getAppSetting('default_currency', DEFAULT_CURRENCY);
    }
    $decimal_places = getAppSetting('decimal_places', DECIMAL_PLACES);
    return $currency . ' ' . number_format((float)$amount, $decimal_places);
}

// Format date
function formatDate($date, $format = 'Y-m-d H:i:s') {
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    return $date->format($format);
}

// Generate a short random merchant order number
function generateOrderNo($length = 10) {
    $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $orderNo = '';
    $max_attempts = 10; // Prevent infinite loops
    for ($attempt = 0; $attempt < $max_attempts; $attempt++) {
        $orderNo = '';
        for ($i = 0; $i < $length; $i++) {
            $orderNo .= $chars[random_int(0, strlen($chars) - 1)];
        }
        // Check if this order_no already exists in the transactions table
        if (!recordExists('transactions', 'order_no = ?', [$orderNo])) {
            return $orderNo;
        }
    }
    // Fallback if unable to generate a unique order number after max_attempts
    logError("Failed to generate a unique order number after $max_attempts attempts.");
    return 'ERR_' . uniqid(); // Return a fallback unique ID
}

// Generate random string
function generateRandomString($length = 10) {
    return bin2hex(random_bytes($length / 2));
}

// Log error
function logError($message, $file = '', $line = '') {
    $log_message = date('Y-m-d H:i:s') . ' - ' . $message;
    if ($file) {
        $log_message .= ' in ' . $file;
    }
    if ($line) {
        $log_message .= ' on line ' . $line;
    }
    $log_message .= PHP_EOL;
    
    error_log($log_message, 3, ERROR_LOG_PATH);
}

// Check if request is AJAX
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// Return JSON response
function jsonResponse($data, $status_code = 200) {
    http_response_code($status_code);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

/**
 * File Upload Functions
 */

// Handle file upload
function handleFileUpload($file, $upload_dir, $allowed_types = ALLOWED_IMAGE_TYPES) {
    if (!isset($file['error']) || is_array($file['error'])) {
        return ['success' => false, 'message' => 'Invalid file upload'];
    }
    
    if ($file['error'] !== UPLOAD_ERR_OK) {
        return ['success' => false, 'message' => 'Upload failed with error code: ' . $file['error']];
    }
    
    if ($file['size'] > MAX_UPLOAD_SIZE) {
        return ['success' => false, 'message' => 'File size exceeds maximum allowed size'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'File type not allowed'];
    }
    
    $filename = uniqid() . '.' . $file_extension;
    $filepath = rtrim(UPLOAD_PATH, '/') . '/' . trim($upload_dir, '/') . '/' . $filename;
    $file_url = BASE_URL . 'uploads/' . trim($upload_dir, '/') . '/' . $filename;
    
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
    
    return ['success' => true, 'filename' => $filename, 'filepath' => $filepath, 'file_url' => $file_url];
}

/**
 * Admin Authentication Functions
 */

// Admin login function
function adminLogin($username, $password, $remember_me = false) {
    try {
        $sql = "SELECT id, username, email, password_hash, role, status 
                FROM admin_users 
                WHERE (username = ? OR email = ?) AND status = 'active'";
        $admin = fetchRow($sql, [$username, $username]);
        
        if (!$admin) {
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
        
        if (!verifyPassword($password, $admin['password_hash'])) {
            return ['success' => false, 'message' => 'Invalid credentials'];
        }
        
        // Set session variables
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['user_type'] = 'admin';
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_email'] = $admin['email'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['last_activity'] = time();
        
        // Update last login
        $update_sql = "UPDATE admin_users SET last_login = NOW() WHERE id = ?";
        executeQuery($update_sql, [$admin['id']]);
        
        // Set remember me cookie if requested
        if ($remember_me) {
            $token = bin2hex(random_bytes(32));
            setcookie('admin_remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
            // Store token in database (you might want to create a remember_tokens table)
        }
        
        return ['success' => true, 'message' => 'Login successful'];
        
    } catch (Exception $e) {
        logError('Admin login error: ' . $e->getMessage());
        return ['success' => false, 'message' => 'Login failed. Please try again.'];
    }
}

// Get current admin info
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    static $admin = null;
    if ($admin === null) {
        $sql = "SELECT id, username, email, role, status, last_login, created_at 
                FROM admin_users WHERE id = ?";
        $admin = fetchRow($sql, [getCurrentAdminId()]);
    }
    
    return $admin;
}

// Check admin permissions
function hasAdminPermission($permission) {
    $admin = getCurrentAdmin();
    if (!$admin) {
        return false;
    }
    
    // Super admin has all permissions
    if ($admin['role'] === 'super_admin') {
        return true;
    }
    
    // Check specific permissions (implement based on your needs)
    return true; // For now, all active admins have all permissions
}

/**
 * Database Helper Functions
 */

// Note: checkDatabase() and checkTable() functions are defined in error_handler.php

/**
 * Fetch a single value from a query result
 */
function fetchValue($sql, $params = []) {
    $row = fetchRow($sql, $params);
    if ($row && is_array($row)) {
        return array_values($row)[0];
    }
    return false;
}

/**
 * Application Specific Functions
 */

// Get user VIP level
function getUserVipLevel($user_id) {
    $sql = "SELECT vl.* FROM vip_levels vl 
            JOIN users u ON u.vip_level = vl.level 
            WHERE u.id = ?";
    return fetchRow($sql, [$user_id]);
}

// Get app settings
function getAppSettings() {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        $results = fetchAll("SELECT `key`, `value` FROM settings");
        if ($results) {
            foreach ($results as $row) {
                $settings[$row['key']] = $row['value'];
            }
        }
    }
    
    return $settings;
}

function getSettings($category) {
    $settings = [];
    $results = fetchAll("SELECT `key`, `value` FROM settings WHERE category = ?", [$category]);
    if ($results) {
        foreach ($results as $row) {
            $settings[$row['key']] = $row['value'];
        }
    }
    return $settings;
}

function updateSetting($key, $value) {
    $sql = "INSERT INTO settings (`key`, `value`) VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE `value` = VALUES(`value`)";
    return executeQuery($sql, [$key, $value]) !== false;
}

// Get specific app setting
function getAppSetting($key, $default = null) {
    $settings = getAppSettings();
    return $settings[$key] ?? $default;
}

/**
 * Updates a user's credit score.
 */
function updateCreditScore($user_id, $points) {
    try {
        $db = getDB();
        $stmt = $db->prepare("UPDATE users SET credit_score = credit_score + ? WHERE id = ?");
        $stmt->execute([$points, $user_id]);
        return true;
    } catch (PDOException $e) {
        logError("Failed to update credit score for user $user_id: " . $e->getMessage());
        return false;
    }
}

/**
 * Adjusts user balance and logs the transaction.
 */
function adjustUserBalance($user_id, $amount, $type, $admin_id = null, $order_no = null, $payment_channel = null, $credited_by = null, $state = 'completed') {
    logError("Adjusting balance for user $user_id, amount $amount, type $type, admin_id $admin_id, order_no $order_no, payment_channel $payment_channel, credited_by $credited_by, state $state");
    try {
        beginTransaction();

        $user = fetchRow('SELECT balance FROM users WHERE id = ?', [$user_id]);
        if (!$user) {
            throw new Exception('User not found.');
        }

        $old_balance = $user['balance'];
        $new_balance = $old_balance;
        
        $tx_type = ($type === 'addition') ? 'admin_credit' : 'admin_deduction';
        $description = ($type === 'addition') ? 'Admin Top-up' : 'Admin Deduction';

        if ($type === 'addition') {
            $new_balance += $amount;
        } elseif ($type === 'deduction') {
            if ($new_balance < $amount) {
                throw new Exception('Insufficient balance for deduction.');
            }
            $new_balance -= $amount;
        } else {
            throw new Exception('Invalid adjustment type.');
        }

        updateRecord('users', ['balance' => $new_balance], 'id = ?', [$user_id]);

        $transaction_data = [
            'user_id' => $user_id,
            'type' => $tx_type, // Use admin_credit or admin_deduction
            'amount' => $amount,
            'balance_before' => $old_balance,
            'balance_after' => $new_balance,
            'status' => $state, // Should be 'completed' for admin adjustments
            'description' => $description,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        if ($order_no !== null) {
            $transaction_data['order_no'] = $order_no;
        }
        if ($payment_channel !== null) {
            $transaction_data['payment_channel'] = $payment_channel;
        }
        if ($credited_by !== null) {
            $transaction_data['credited_by'] = $credited_by;
        }
        if ($admin_id !== null) {
            $transaction_data['processed_by'] = $admin_id; // Assuming processed_by stores admin_id
        }

        insertRecord('transactions', $transaction_data);

        commitTransaction();
        return true;
    } catch (Exception $e) {
        rollbackTransaction();
        logError('Failed to adjust user balance: ' . $e->getMessage());
        return false;
    }
}

/**
 * Records a transaction for a user.
 */
function recordTransaction($user_id, $amount, $type, $description, $status = 'completed') {
    try {
        $db = getDB();
        $stmt = $db->prepare("INSERT INTO transactions (user_id, type, amount, status, description) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([$user_id, $type, $amount, $status, $description]);
        return true;
    } catch (PDOException $e) {
        logError("Failed to record transaction: " . $e->getMessage());
        return false;
    }
}

/**
 * Renders pagination controls.
 */
function renderPagination($page, $total_pages, $base_query = '') {
    // Ensure parameters are integers to prevent string-int operation errors
    $page = (int)$page;
    $total_pages = (int)$total_pages;

    if ($total_pages <= 1) {
        return;
    }

    parse_str($base_query, $query_params);

    echo '<nav aria-label="Page navigation">';
    echo '<ul class="pagination justify-content-end">';

    // Previous button
    $query_params['page'] = $page - 1;
    $prev_link = '?' . http_build_query($query_params);
    echo '<li class="page-item ' . ($page <= 1 ? 'disabled' : '') . '">';
    echo '<a class="page-link" href="' . ($page <= 1 ? '#' : $prev_link) . '">Previous</a>';
    echo '</li>';

    // Page numbers logic
    $window = 2;
    if ($total_pages <= (2 * $window) + 5) {
        for ($i = 1; $i <= $total_pages; $i++) {
            $query_params['page'] = $i;
            echo '<li class="page-item ' . ($i == $page ? 'active' : '') . '"><a class="page-link" href="?' . http_build_query($query_params) . '">' . $i . '</a></li>';
        }
    } else {
        $query_params['page'] = 1;
        echo '<li class="page-item ' . (1 == $page ? 'active' : '') . '"><a class="page-link" href="?' . http_build_query($query_params) . '">1</a></li>';

        if ($page > $window + 2) {
            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }

        $start = max(2, $page - $window);
        $end = min($total_pages - 1, $page + $window);

        for ($i = $start; $i <= $end; $i++) {
            $query_params['page'] = $i;
            echo '<li class="page-item ' . ($i == $page ? 'active' : '') . '"><a class="page-link" href="?' . http_build_query($query_params) . '">' . $i . '</a></li>';
        }

        if ($page < $total_pages - $window - 1) {
            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
        }
        
        $query_params['page'] = $total_pages;
        echo '<li class="page-item ' . ($total_pages == $page ? 'active' : '') . '"><a class="page-link" href="?' . http_build_query($query_params) . '">' . $total_pages . '</a></li>';
    }

    // Next button
    $query_params['page'] = $page + 1;
    $next_link = '?' . http_build_query($query_params);
    echo '<li class="page-item ' . ($page >= $total_pages ? 'disabled' : '') . '">';
    echo '<a class="page-link" href="' . ($page >= $total_pages ? '#' : $next_link) . '">Next</a>';
    echo '</li>';

    echo '</ul>';
    echo '</nav>';
}
?>
