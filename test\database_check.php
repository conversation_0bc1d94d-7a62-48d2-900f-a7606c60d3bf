<?php
/**
 * Database Structure Check for Bamboo
 */

define('BAMBOO_APP', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Check - Bamboo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ff6900 100%); min-height: 100vh; }
        .container { max-width: 900px; margin: 2rem auto; }
        .card { margin-bottom: 1rem; }
        .table-success { background-color: #d4edda; }
        .table-danger { background-color: #f8d7da; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2><i class="bi bi-database"></i> Database Structure Check</h2>
            </div>
            <div class="card-body">
                
                <?php
                try {
                    $db = getDB();
                    echo '<div class="alert alert-success">✅ Database connection successful!</div>';
                    
                    // Check if database exists and is selected
                    $stmt = $db->query("SELECT DATABASE() as db_name");
                    $result = $stmt->fetch();
                    echo '<p><strong>Connected to database:</strong> ' . ($result['db_name'] ?? 'None') . '</p>';
                    
                    // Get all tables
                    $stmt = $db->query("SHOW TABLES");
                    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                    
                    echo '<h4>Database Tables (' . count($tables) . ' found)</h4>';
                    
                    if (empty($tables)) {
                        echo '<div class="alert alert-warning">';
                        echo '⚠️ No tables found! Please import database_migration.sql';
                        echo '<br><br><strong>Steps to fix:</strong>';
                        echo '<ol>';
                        echo '<li>Open phpMyAdmin: <a href="http://localhost/phpMyAdmin" target="_blank">http://localhost/phpMyAdmin</a></li>';
                        echo '<li>Select the "matchmaking" database</li>';
                        echo '<li>Go to "Import" tab</li>';
                        echo '<li>Choose file: database_migration.sql</li>';
                        echo '<li>Click "Go"</li>';
                        echo '</ol>';
                        echo '</div>';
                    } else {
                        // Expected tables
                        $expected_tables = [
                            'vip_levels', 'users', 'admin_users', 'products', 
                            'product_categories', 'tasks', 'task_completions', 
                            'transactions', 'notifications', 'user_sessions', 'settings'
                        ];
                        
                        echo '<div class="table-responsive">';
                        echo '<table class="table table-striped">';
                        echo '<thead><tr><th>Table Name</th><th>Status</th><th>Record Count</th></tr></thead>';
                        echo '<tbody>';
                        
                        foreach ($expected_tables as $table) {
                            $exists = in_array($table, $tables);
                            $count = 0;
                            
                            if ($exists) {
                                try {
                                    $stmt = $db->query("SELECT COUNT(*) as count FROM `$table`");
                                    $result = $stmt->fetch();
                                    $count = $result['count'];
                                } catch (Exception $e) {
                                    $count = 'Error';
                                }
                            }
                            
                            $class = $exists ? 'table-success' : 'table-danger';
                            $status = $exists ? '✅ Exists' : '❌ Missing';
                            
                            echo "<tr class='$class'>";
                            echo "<td><strong>$table</strong></td>";
                            echo "<td>$status</td>";
                            echo "<td>" . ($exists ? $count : '-') . "</td>";
                            echo "</tr>";
                        }
                        
                        echo '</tbody></table>';
                        echo '</div>';
                        
                        // Check for extra tables
                        $extra_tables = array_diff($tables, $expected_tables);
                        if (!empty($extra_tables)) {
                            echo '<h5>Additional Tables Found:</h5>';
                            echo '<ul>';
                            foreach ($extra_tables as $table) {
                                echo "<li>$table</li>";
                            }
                            echo '</ul>';
                        }
                        
                        // Sample data check
                        echo '<h4>Sample Data Check</h4>';
                        
                        // Check VIP levels
                        if (in_array('vip_levels', $tables)) {
                            $stmt = $db->query("SELECT level, name, min_balance FROM vip_levels ORDER BY level LIMIT 3");
                            $vip_levels = $stmt->fetchAll();
                            
                            if (!empty($vip_levels)) {
                                echo '<div class="alert alert-info">';
                                echo '<strong>VIP Levels Sample:</strong><br>';
                                foreach ($vip_levels as $vip) {
                                    echo "VIP {$vip['level']}: {$vip['name']} (Min Balance: {$vip['min_balance']})<br>";
                                }
                                echo '</div>';
                            }
                        }
                        
                        // Check settings
                        if (in_array('settings', $tables)) {
                            $stmt = $db->query("SELECT `key`, `value` FROM settings LIMIT 5");
                            $settings = $stmt->fetchAll();
                            
                            if (!empty($settings)) {
                                echo '<div class="alert alert-info">';
                                echo '<strong>Settings Sample:</strong><br>';
                                foreach ($settings as $setting) {
                                    echo "{$setting['key']}: {$setting['value']}<br>";
                                }
                                echo '</div>';
                            }
                        }
                    }
                    
                } catch (Exception $e) {
                    echo '<div class="alert alert-danger">';
                    echo '❌ Database connection failed: ' . $e->getMessage();
                    echo '<br><br><strong>Common solutions:</strong>';
                    echo '<ul>';
                    echo '<li>Start MAMP and ensure MySQL is running (green light)</li>';
                    echo '<li>Create database "matchmaking" in phpMyAdmin</li>';
                    echo '<li>Check database credentials in includes/config.php</li>';
                    echo '<li>Verify MySQL port (default: 3306)</li>';
                    echo '</ul>';
                    echo '</div>';
                }
                ?>
                
                <div class="mt-4">
                    <a href="../" class="btn btn-primary">← Back to App</a>
                    <a href="test_connection.php" class="btn btn-outline-primary">Full System Test</a>
                    <a href="quick_test.php" class="btn btn-outline-secondary">Quick Test</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>