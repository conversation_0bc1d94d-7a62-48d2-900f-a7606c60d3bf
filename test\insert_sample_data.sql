-- Insert sample users
INSERT INTO users (username, phone, email, password_hash, withdrawal_pin_hash, gender, invitation_code, balance, commission_balance, frozen_balance, total_deposited, total_withdrawn, total_commission_earned, vip_level, tasks_completed_today, last_task_date, status, email_verified, phone_verified, avatar_url, referral_count, last_login, login_count, created_at, updated_at, invited_by) VALUES
('alice', '1234567890', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'female', 'ALICE01', 100.00, 10.00, 0.00, 200.00, 50.00, 20.00, 1, 2, CURDATE(), 'active', 1, 1, NULL, 0, NOW(), 5, NOW(), NOW(), NULL),
('bob', '2345678901', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'male', 'BOB01', 150.00, 15.00, 0.00, 300.00, 100.00, 30.00, 2, 1, CURDATE(), 'active', 1, 1, NULL, 1, NOW(), 10, NOW(), NOW(), 1);

-- Insert sample tasks
INSERT INTO tasks (user_id, product_id, amount, commission_earned, base_commission, vip_bonus, status, assigned_at, started_at, completed_at, expires_at, submission_data, admin_notes) VALUES
(1, 1, 50.00, 5.00, 5.00, 0.00, 'completed', NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), NULL, 'Task completed by Alice'),
(2, 2, 80.00, 8.00, 8.00, 0.00, 'completed', NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), NULL, 'Task completed by Bob');

-- Insert sample transactions
INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, status, payment_method, transaction_id, created_at, updated_at) VALUES
(1, 'deposit', 200.00, 0.00, 200.00, 'completed', 'bank', 'TXN000001', NOW(), NOW()),
(2, 'deposit', 300.00, 0.00, 300.00, 'completed', 'bank', 'TXN000002', NOW(), NOW()),
(1, 'withdrawal', 50.00, 200.00, 150.00, 'completed', 'bank', 'TXN000003', NOW(), NOW()),
(2, 'withdrawal', 100.00, 300.00, 200.00, 'completed', 'bank', 'TXN000004', NOW(), NOW());
