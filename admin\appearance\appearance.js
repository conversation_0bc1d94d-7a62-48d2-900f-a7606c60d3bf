/**
 * Bamboo Web Application - Appearance Settings JavaScript
 * Company: Notepadsly
 * Version: 1.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize appearance settings
    initializeAppearanceSettings();
});

function initializeAppearanceSettings() {
    // Color input synchronization
    setupColorInputs();

    // Live preview updates
    setupLivePreview();

    // Form handlers
    setupFormHandlers();

    // Add color palette buttons
    addColorPaletteButtons();

    // Initial preview update
    updateLivePreview();
}

function setupColorInputs() {
    // Sync color picker with text input
    const colorInputs = document.querySelectorAll('.form-control-color');
    
    colorInputs.forEach(colorInput => {
        const textInput = document.querySelector(`[data-color-input="${colorInput.id}"]`);
        
        if (textInput) {
            // Update text when color picker changes
            colorInput.addEventListener('input', function() {
                textInput.value = this.value.toUpperCase();
                updateLivePreview();
            });
            
            // Update color picker when text changes
            textInput.addEventListener('input', function() {
                if (isValidHexColor(this.value)) {
                    colorInput.value = this.value;
                    updateLivePreview();
                }
            });
            
            // Validate hex color on blur
            textInput.addEventListener('blur', function() {
                if (!isValidHexColor(this.value)) {
                    this.value = colorInput.value.toUpperCase();
                }
            });
        }
    });
}

function setupLivePreview() {
    // Update preview when any form input changes
    const formInputs = document.querySelectorAll('#appearanceForm input, #appearanceForm select');
    
    formInputs.forEach(input => {
        input.addEventListener('change', updateLivePreview);
        input.addEventListener('input', debounce(updateLivePreview, 300));
    });
}

function setupFormHandlers() {
    // Reset to default button
    const resetButton = document.getElementById('resetToDefault');
    if (resetButton) {
        resetButton.addEventListener('click', resetToDefault);
    }
    
    // Preview changes button
    const previewButton = document.getElementById('previewChanges');
    if (previewButton) {
        previewButton.addEventListener('click', previewChanges);
    }
    
    // Form submission
    const form = document.getElementById('appearanceForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmission);
    }
}

function updateLivePreview() {
    const settings = getCurrentSettings();

    // Update gradient preview
    updateGradientPreview(settings);

    // Update mini dashboard preview
    updateMiniDashboard(settings);

    // Update dynamic theme if available
    if (window.BambooDynamicTheme) {
        window.BambooDynamicTheme.updateSettings({
            primaryColor: settings.primaryColor,
            secondaryColor: settings.secondaryColor,
            accentColor: settings.accentColor,
            gradientStart: settings.gradientStart,
            gradientEnd: settings.gradientEnd,
            cardBackground: settings.cardBackground,
            sidebarStyle: settings.sidebarStyle,
            cardShadow: settings.cardShadow,
            borderRadius: settings.borderRadius,
            themeMode: settings.themeMode
        });
    }
}

function getCurrentSettings() {
    return {
        primaryColor: document.getElementById('primary_color')?.value || '#ff6900',
        secondaryColor: document.getElementById('secondary_color')?.value || '#ffffff',
        accentColor: document.getElementById('accent_color')?.value || '#007bff',
        gradientStart: document.getElementById('gradient_start')?.value || '#ff6900',
        gradientEnd: document.getElementById('gradient_end')?.value || '#ff8533',
        cardBackground: document.getElementById('card_background')?.value || '#ffffff',
        sidebarStyle: document.getElementById('sidebar_style')?.value || 'gradient',
        sidebarTextColor: document.getElementById('sidebar_text_color')?.value || '#ffffff',
        cardShadow: document.getElementById('card_shadow')?.value || 'subtle',
        borderRadius: document.getElementById('border_radius')?.value || '0.5rem',
        buttonStyle: document.getElementById('button_style')?.value || 'gradient',
        themeMode: document.getElementById('theme_mode')?.value || 'light'
    };
}

function updateGradientPreview(settings) {
    const gradientPreview = document.getElementById('gradientPreview');
    if (gradientPreview) {
        gradientPreview.style.background = `linear-gradient(135deg, ${settings.gradientStart} 0%, ${settings.gradientEnd} 100%)`;
    }
}

function updateMiniDashboard(settings) {
    const miniSidebar = document.getElementById('previewSidebar');
    const miniCards = document.querySelectorAll('.mini-card');

    if (miniSidebar) {
        // Update sidebar style
        switch (settings.sidebarStyle) {
            case 'gradient':
                miniSidebar.style.background = `linear-gradient(180deg, ${settings.gradientStart} 0%, ${settings.gradientEnd} 100%)`;
                miniSidebar.style.color = settings.sidebarTextColor;
                break;
            case 'solid':
                miniSidebar.style.background = settings.primaryColor;
                miniSidebar.style.color = settings.sidebarTextColor;
                break;
            case 'light':
                miniSidebar.style.background = '#f8f9fa';
                miniSidebar.style.color = '#495057';
                break;
        }
    }
    
    // Update card styles
    miniCards.forEach(card => {
        card.style.background = settings.cardBackground;
        card.style.borderRadius = settings.borderRadius;
        
        // Update shadow
        switch (settings.cardShadow) {
            case 'none':
                card.style.boxShadow = 'none';
                break;
            case 'subtle':
                card.style.boxShadow = '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
                break;
            case 'medium':
                card.style.boxShadow = '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
                break;
            case 'strong':
                card.style.boxShadow = '0 1rem 3rem rgba(0, 0, 0, 0.175)';
                break;
        }
    });
}

function resetToDefault() {
    if (confirm('Are you sure you want to reset all appearance settings to default? This action cannot be undone.')) {
        // Set default values
        const defaults = {
            primary_color: '#ff6900',
            secondary_color: '#ffffff',
            accent_color: '#007bff',
            gradient_start: '#ff6900',
            gradient_end: '#ff8533',
            card_background: '#ffffff',
            sidebar_style: 'gradient',
            card_shadow: 'subtle',
            border_radius: '0.5rem',
            theme_mode: 'light'
        };
        
        // Update form inputs
        Object.keys(defaults).forEach(key => {
            const input = document.getElementById(key);
            if (input) {
                input.value = defaults[key];
                
                // Update corresponding text input for color fields
                const textInput = document.querySelector(`[data-color-input="${key}"]`);
                if (textInput) {
                    textInput.value = defaults[key].toUpperCase();
                }
            }
        });
        
        // Update preview
        updateLivePreview();
        
        // Show success message
        showNotification('Settings reset to default values', 'success');
    }
}

function previewChanges() {
    const settings = getCurrentSettings();
    
    // Apply temporary styles to the current page
    applyTemporaryStyles(settings);
    
    // Show notification
    showNotification('Preview applied! Refresh the page to revert changes.', 'info');
}

function applyTemporaryStyles(settings) {
    // Create or update temporary style element
    let styleElement = document.getElementById('temp-appearance-styles');
    if (!styleElement) {
        styleElement = document.createElement('style');
        styleElement.id = 'temp-appearance-styles';
        document.head.appendChild(styleElement);
    }
    
    // Generate CSS
    const css = `
        :root {
            --admin-primary: ${settings.primaryColor} !important;
            --admin-secondary: ${settings.secondaryColor} !important;
            --admin-accent: ${settings.accentColor} !important;
            --admin-gradient-end: ${settings.gradientEnd} !important;
            --admin-primary-rgb: ${hexToRgb(settings.primaryColor)} !important;
            --admin-accent-rgb: ${hexToRgb(settings.accentColor)} !important;
            --admin-border-radius: ${settings.borderRadius} !important;
            --dynamic-gradient-start: ${settings.gradientStart} !important;
            --dynamic-gradient-end: ${settings.gradientEnd} !important;
            --dynamic-primary: ${settings.primaryColor} !important;
            --dynamic-accent: ${settings.accentColor} !important;
            --dynamic-primary-rgb: ${hexToRgb(settings.primaryColor)} !important;
            --dynamic-accent-rgb: ${hexToRgb(settings.accentColor)} !important;
        }

        .admin-sidebar {
            background: ${settings.sidebarStyle === 'gradient'
                ? `linear-gradient(180deg, ${settings.gradientStart} 0%, ${settings.gradientEnd} 100%)`
                : settings.sidebarStyle === 'solid'
                ? settings.primaryColor
                : '#f8f9fa'} !important;
            ${settings.sidebarStyle === 'light' ? 'color: #495057 !important;' : `color: ${settings.sidebarTextColor} !important;`}
        }

        .card {
            background: ${settings.cardBackground} !important;
            border-radius: ${settings.borderRadius} !important;
            box-shadow: ${getShadowValue(settings.cardShadow)} !important;
        }

        .btn-primary {
            ${getButtonStyle(settings)} !important;
            border: none !important;
        }
    `;
    
    styleElement.textContent = css;
}

function getShadowValue(shadowType) {
    switch (shadowType) {
        case 'none': return 'none';
        case 'subtle': return '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
        case 'medium': return '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
        case 'strong': return '0 1rem 3rem rgba(0, 0, 0, 0.175)';
        default: return '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
    }
}

function hexToRgb(hex) {
    // Remove the hash if present
    hex = hex.replace('#', '');

    // Parse the hex values
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    return `${r}, ${g}, ${b}`;
}

function getButtonStyle(settings) {
    switch (settings.buttonStyle) {
        case 'primary':
            return `background: ${settings.primaryColor}`;
        case 'accent':
            return `background: ${settings.accentColor}`;
        case 'gradient':
        default:
            return `background: linear-gradient(135deg, ${settings.primaryColor} 0%, ${settings.accentColor} 100%)`;
    }
}

function handleFormSubmission(event) {
    // Add loading state
    const submitButton = event.target.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Saving...';
    }
    
    // Form will submit normally, this just provides user feedback
}

function isValidHexColor(hex) {
    return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(hex);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Color palette suggestions
const colorPalettes = {
    default: {
        primary: '#ff6900',
        secondary: '#ffffff',
        accent: '#007bff',
        gradientStart: '#ff6900',
        gradientEnd: '#ff8533'
    },
    blue: {
        primary: '#007bff',
        secondary: '#ffffff',
        accent: '#0056b3',
        gradientStart: '#007bff',
        gradientEnd: '#0056b3'
    },
    green: {
        primary: '#28a745',
        secondary: '#ffffff',
        accent: '#1e7e34',
        gradientStart: '#28a745',
        gradientEnd: '#1e7e34'
    },
    purple: {
        primary: '#6f42c1',
        secondary: '#ffffff',
        accent: '#5a32a3',
        gradientStart: '#6f42c1',
        gradientEnd: '#5a32a3'
    }
};

function applyColorPalette(paletteName) {
    const palette = colorPalettes[paletteName];
    if (palette) {
        Object.keys(palette).forEach(key => {
            const input = document.getElementById(key.replace(/([A-Z])/g, '_$1').toLowerCase());
            if (input) {
                input.value = palette[key];
                const textInput = document.querySelector(`[data-color-input="${input.id}"]`);
                if (textInput) {
                    textInput.value = palette[key].toUpperCase();
                }
            }
        });
        updateLivePreview();

        // Use dynamic theme animation if available
        if (window.BambooDynamicTheme && palette.primary) {
            const currentPrimary = window.BambooDynamicTheme.getCurrentSettings().primaryColor;
            window.BambooDynamicTheme.animateColorTransition(currentPrimary, palette.primary, 800);
        }
    }
}

// Add color palette buttons to the appearance page
function addColorPaletteButtons() {
    const paletteContainer = document.createElement('div');
    paletteContainer.className = 'color-palette-container mt-3';
    paletteContainer.innerHTML = `
        <h6 class="mb-3">Quick Color Palettes</h6>
        <div class="row g-2">
            <div class="col-auto">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="applyColorPalette('default')">
                    <span class="color-dot" style="background: #ff6900;"></span> Default
                </button>
            </div>
            <div class="col-auto">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="applyColorPalette('blue')">
                    <span class="color-dot" style="background: #007bff;"></span> Blue
                </button>
            </div>
            <div class="col-auto">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="applyColorPalette('green')">
                    <span class="color-dot" style="background: #28a745;"></span> Green
                </button>
            </div>
            <div class="col-auto">
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="applyColorPalette('purple')">
                    <span class="color-dot" style="background: #6f42c1;"></span> Purple
                </button>
            </div>
        </div>
    `;

    // Add to the color settings card
    const colorCard = document.querySelector('.card .card-body');
    if (colorCard) {
        colorCard.appendChild(paletteContainer);
    }
}
