<?php
define('BAMBOO_APP', true);
require_once 'includes/config.php';
require_once 'includes/functions.php';
require_once 'includes/database.php';

// Assuming a user with ID 1 exists for testing
$test_user_id = 1;

echo "Attempting to delete user with ID: " . $test_user_id . "\n";
logError("Attempting direct delete of user with ID: " . $test_user_id, __FILE__, __LINE__);

// First, delete related records from other tables if they exist
$deleted_transactions = deleteRecord('transactions', 'user_id = ?', [$test_user_id]);
logError("Deleted transactions for user ID " . $test_user_id . ": " . ($deleted_transactions ? "true" : "false"), __FILE__, __LINE__);

$deleted_tasks = deleteRecord('tasks', 'user_id = ?', [$test_user_id]);
logError("Deleted tasks for user ID " . $test_user_id . ": " . ($deleted_tasks ? "true" : "false"), __FILE__, __LINE__);

$deleted_sessions = deleteRecord('user_sessions', 'user_id = ?', [$test_user_id]);
logError("Deleted user sessions for user ID " . $test_user_id . ": " . ($deleted_sessions ? "true" : "false"), __FILE__, __LINE__);

// Check for tables that might not exist in all installations
// user_salaries
if (tableExists('user_salaries')) {
    $deleted_salaries = deleteRecord('user_salaries', 'user_id = ?', [$test_user_id]);
    logError("Deleted user salaries for user ID " . $test_user_id . ": " . ($deleted_salaries ? "true" : "false"), __FILE__, __LINE__);
} else {
    logError("Table 'user_salaries' does not exist, skipping deletion for user ID " . $test_user_id, __FILE__, __LINE__);
}

// withdrawal_quotes
if (tableExists('withdrawal_quotes')) {
    $deleted_withdrawal_quotes = deleteRecord('withdrawal_quotes', 'user_id = ?', [$test_user_id]);
    logError("Deleted withdrawal quotes for user ID " . $test_user_id . ": " . ($deleted_withdrawal_quotes ? "true" : "false"), __FILE__, __LINE__);
} else {
    logError("Table 'withdrawal_quotes' does not exist, skipping deletion for user ID " . $test_user_id, __FILE__, __LINE__);
}

// negative_settings
if (tableExists('negative_settings')) {
    $deleted_negative_settings = deleteRecord('negative_settings', 'user_id = ?', [$test_user_id]);
    logError("Deleted negative settings for user ID " . $test_user_id . ": " . ($deleted_negative_settings ? "true" : "false"), __FILE__, __LINE__);
} else {
    logError("Table 'negative_settings' does not exist, skipping deletion for user ID " . $test_user_id, __FILE__, __LINE__);
}

$deleted_notifications = deleteRecord('notifications', 'target_user_id = ?', [$test_user_id]);
logError("Deleted notifications for user ID " . $test_user_id . ": " . ($deleted_notifications ? "true" : "false"), __FILE__, __LINE__);


// Finally, delete the user record
$deleted_user = deleteRecord('users', 'id = ?', [$test_user_id]);

if ($deleted_user) {
    echo "User with ID " . $test_user_id . " deleted successfully.\n";
    logError("User ID: " . $test_user_id . " deleted successfully.", __FILE__, __LINE__);
} else {
    echo "Failed to delete user with ID " . $test_user_id . ".\n";
    logError("Failed to delete user ID: " . $test_user_id . ". deleteRecord returned false.", __FILE__, __LINE__);
}

?>
