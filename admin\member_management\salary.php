<?php
/**
 * Bamboo Web Application - Salary Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, balance, avatar, tasks_completed_today FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

// Get total salary paid to user
$total_salary = fetchRow("SELECT SUM(amount) as total FROM user_salaries WHERE user_id = ? AND status = 'paid'", [$user_id]);
$total_salary_amount = $total_salary ? $total_salary['total'] : 0;

// Get appearance settings for gradient colors
$gradient_start = getAppSetting('appearance_gradient_start', '#ff6900');
$gradient_end = getAppSetting('appearance_gradient_end', '#ff8533');

$page_title = 'Salary Management for ' . htmlspecialchars($user['username']);
$additional_css = [BASE_URL . 'admin/assets/css/member-details.css'];
include '../includes/admin_header.php';

// Handle form submission for paying salary
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $amount = (float)($_POST['amount'] ?? 0);
        $notes = sanitizeInput($_POST['notes']);

        if ($amount > 0) {
            insertRecord('user_salaries', [
                'user_id' => $user_id,
                'amount' => $amount,
                'notes' => $notes,
                'status' => 'paid', // Or 'pending_approval'
                'admin_id_processed' => $_SESSION['admin_id']
            ]);
            // Also adjust user balance
            adjustUserBalance($user_id, $amount, 'addition');
            showSuccess('Salary paid successfully!');
        } else {
            showError('Invalid amount.');
        }
        redirect('admin/member_management/salary.php?id=' . $user_id);
        exit();
    }
}

$salaries = fetchAll('SELECT * FROM user_salaries WHERE user_id = ? ORDER BY created_at DESC', [$user_id]);

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar-header me-3">
                            <?php if (!empty($user['avatar'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/avatars/' . $user['avatar']; ?>" alt="Avatar" class="header-avatar">
                            <?php else: ?>
                                <div class="header-avatar-initials">
                                    <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h1 class="h3 mb-0">
                                <i class="bi bi-currency-dollar text-success me-2"></i>
                                Salary Management: <?php echo htmlspecialchars($user['username']); ?>
                            </h1>
                            <small class="text-muted">Manage salary payments and history</small>
                        </div>
                    </div>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-circle me-2"></i>User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Username</div>
                                    <div class="info-value"><?php echo htmlspecialchars($user['username']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Current Balance</div>
                                    <div class="info-value text-success fw-bold"><?php echo formatCurrency($user['balance']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Total Salary Paid</div>
                                    <div class="info-value text-success fw-bold"><?php echo formatCurrency($total_salary_amount); ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header text-white" style="background: linear-gradient(135deg, <?php echo $gradient_start; ?>, <?php echo $gradient_end; ?>);">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-cash-coin me-2"></i>Pay Salary
                        </h5>
                        <small class="opacity-75">Add salary payment to user account</small>
                    </div>
                    <div class="card-body">
                        <form action="salary.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="amount" class="form-label">
                                        <i class="bi bi-currency-dollar text-success me-1"></i>Salary Amount
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="amount" name="amount"
                                               step="0.01" min="0.01" placeholder="0.00" required>
                                    </div>
                                    <div class="form-text">Amount to add to user's balance</div>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="notes" class="form-label">
                                        <i class="bi bi-chat-text text-primary me-1"></i>Notes
                                    </label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3"
                                              placeholder="Optional notes about this salary payment..."></textarea>
                                    <div class="form-text">Optional description or reason for payment</div>
                                </div>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-cash-coin me-2"></i>Pay Salary
                                </button>
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Salary History</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($salaries)): ?>
                            <p class="text-muted">No salary records found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>S/N</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Notes</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $sn = 1; foreach ($salaries as $salary): ?>
                                            <tr>
                                                <td><?php echo $sn++; ?></td>
                                                <td><span class="text-success fw-bold"><?php echo formatCurrency($salary['amount']); ?></span></td>
                                                <td>
                                                    <span class="badge bg-<?php echo $salary['status'] === 'paid' ? 'success' : 'warning'; ?>">
                                                        <?php echo ucfirst($salary['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars($salary['notes']); ?></td>
                                                <td><?php echo formatDate($salary['created_at']); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
