mysqldump: [Warning] Using a password on the command line interface can be insecure.
-- My<PERSON><PERSON> dump 10.13  Distrib 5.7.24, for Win64 (x86_64)
--
-- Host: localhost    Database: matchmaking
-- ------------------------------------------------------
-- Server version	5.7.24

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Temporary table structure for view `admin_user_stats`
--

DROP TABLE IF EXISTS `admin_user_stats`;
/*!50001 DROP VIEW IF EXISTS `admin_user_stats`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `admin_user_stats` AS SELECT 
 1 AS `total_users`,
 1 AS `active_users`,
 1 AS `new_today`,
 1 AS `total_balance`,
 1 AS `total_deposits`,
 1 AS `total_withdrawals`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `role` enum('super_admin','admin','moderator') NOT NULL DEFAULT 'admin',
  `permissions` json DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_role` (`role`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_users`
--

LOCK TABLES `admin_users` WRITE;
/*!40000 ALTER TABLE `admin_users` DISABLE KEYS */;
INSERT INTO `admin_users` VALUES (1,'admin','<EMAIL>','$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi','super_admin',NULL,NULL,'active','2025-06-27 08:23:08','2025-06-27 08:23:08');
/*!40000 ALTER TABLE `admin_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `customer_service_contacts`
--

DROP TABLE IF EXISTS `customer_service_contacts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `customer_service_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `link` varchar(500) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `customer_service_contacts`
--

LOCK TABLES `customer_service_contacts` WRITE;
/*!40000 ALTER TABLE `customer_service_contacts` DISABLE KEYS */;
INSERT INTO `customer_service_contacts` VALUES (1,'1','1','2025-06-27 05:56:19'),(2,'WORLD BAMBOO CUSTOMER SERVICE TG1','https://t.me/BambooCS0','2025-06-27 09:18:38');
/*!40000 ALTER TABLE `customer_service_contacts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `negative_settings`
--

DROP TABLE IF EXISTS `negative_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `negative_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `trigger_task_number` int(11) NOT NULL,
  `product_id_override` int(11) NOT NULL,
  `override_amount` decimal(15,2) NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `is_triggered` tinyint(1) NOT NULL DEFAULT '0',
  `admin_id_created` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `product_id_override` (`product_id_override`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `negative_settings`
--

LOCK TABLES `negative_settings` WRITE;
/*!40000 ALTER TABLE `negative_settings` DISABLE KEYS */;
INSERT INTO `negative_settings` VALUES (1,1,11,1,11111.00,1,0,1,'2025-06-27 05:41:27'),(2,4,12,1,1000.00,1,0,1,'2025-06-27 11:16:09');
/*!40000 ALTER TABLE `negative_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `notifications` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('system','user','admin','banner') NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `target_user_id` int(11) DEFAULT NULL,
  `target_vip_level` int(11) DEFAULT NULL,
  `is_global` tinyint(1) NOT NULL DEFAULT '0',
  `is_popup` tinyint(1) NOT NULL DEFAULT '0',
  `is_banner` tinyint(1) NOT NULL DEFAULT '0',
  `banner_color` varchar(7) DEFAULT '#007bff',
  `status` enum('active','inactive','scheduled') NOT NULL DEFAULT 'active',
  `start_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `end_date` timestamp NULL DEFAULT NULL,
  `read_count` int(11) NOT NULL DEFAULT '0',
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `created_by` (`created_by`),
  KEY `idx_type` (`type`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_is_global` (`is_global`),
  KEY `idx_status` (`status`),
  KEY `idx_start_date` (`start_date`),
  CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`target_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `notifications_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES (1,'banner','Welcome','Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!',NULL,NULL,1,0,1,'#007bff','active','2025-06-27 08:23:10',NULL,0,NULL,'2025-06-27 08:23:10','2025-06-27 08:23:10');
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `product_categories`
--

DROP TABLE IF EXISTS `product_categories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `product_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `description` text,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `sort_order` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `product_categories`
--

LOCK TABLES `product_categories` WRITE;
/*!40000 ALTER TABLE `product_categories` DISABLE KEYS */;
INSERT INTO `product_categories` VALUES (1,'Electronics','Electronic devices and gadgets','active',0,'2025-06-27 08:23:08','2025-06-27 08:23:08'),(2,'Fashion','Clothing and accessories','active',0,'2025-06-27 08:23:08','2025-06-27 08:23:08'),(3,'Home & Garden','Home improvement and garden items','active',0,'2025-06-27 08:23:08','2025-06-27 08:23:08'),(4,'Sports','Sports and fitness equipment','active',0,'2025-06-27 08:23:08','2025-06-27 08:23:08'),(5,'Beauty','Beauty and personal care products','active',0,'2025-06-27 08:23:08','2025-06-27 08:23:08');
/*!40000 ALTER TABLE `product_categories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `products`
--

DROP TABLE IF EXISTS `products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `products` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `description` text,
  `image_url` varchar(500) DEFAULT NULL,
  `price` decimal(10,2) NOT NULL,
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '5.00',
  `category_id` int(11) NOT NULL,
  `min_vip_level` int(11) NOT NULL DEFAULT '1',
  `max_daily_assignments` int(11) NOT NULL DEFAULT '100',
  `weight` int(11) NOT NULL DEFAULT '1',
  `stock` int(11) NOT NULL DEFAULT '0',
  `status` enum('active','inactive','out_of_stock') NOT NULL DEFAULT 'active',
  `total_assignments` int(11) NOT NULL DEFAULT '0',
  `total_completions` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_min_vip_level` (`min_vip_level`),
  KEY `idx_price` (`price`),
  KEY `idx_product_category_status` (`category_id`,`status`),
  CONSTRAINT `products_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `product_categories` (`id`) ON DELETE CASCADE,
  CONSTRAINT `products_ibfk_2` FOREIGN KEY (`min_vip_level`) REFERENCES `vip_levels` (`level`) ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `products`
--

LOCK TABLES `products` WRITE;
/*!40000 ALTER TABLE `products` DISABLE KEYS */;
INSERT INTO `products` VALUES (1,'iPhone 15 Pro','Latest iPhone with advanced features','http://localhost/bamboo/uploads/products/685e5bfbdde32.jpg',999.00,5.00,1,1,100,1,100,'active',0,0,'2025-06-27 08:23:10','2025-06-27 06:53:16'),(2,'Samsung Galaxy S24','Premium Android smartphone','/uploads/products/galaxy-s24.jpg',899.00,4.50,1,1,100,1,100,'active',0,0,'2025-06-27 08:23:10','2025-06-27 08:23:10'),(3,'Nike Air Max','Comfortable running shoes','/uploads/products/nike-airmax.jpg',150.00,8.00,4,1,100,1,50,'active',0,0,'2025-06-27 08:23:10','2025-06-27 08:23:10'),(4,'Luxury Watch','Premium timepiece','/uploads/products/luxury-watch.jpg',2500.00,3.00,2,3,100,1,10,'active',0,0,'2025-06-27 08:23:10','2025-06-27 08:23:10');
/*!40000 ALTER TABLE `products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `key` varchar(100) NOT NULL,
  `value` text NOT NULL,
  `type` enum('string','integer','float','boolean','json') NOT NULL DEFAULT 'string',
  `description` text,
  `category` varchar(50) NOT NULL DEFAULT 'general',
  `is_public` tinyint(1) NOT NULL DEFAULT '0',
  `updated_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `key` (`key`),
  KEY `updated_by` (`updated_by`),
  KEY `idx_key` (`key`),
  KEY `idx_category` (`category`),
  CONSTRAINT `settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES (1,'app_name','Bamboo','string','Application name','general',1,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(2,'app_logo','/assets/images/logo.png','string','Application logo URL','general',1,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(3,'welcome_bonus','10.00','float','Welcome bonus for new users','financial',0,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(4,'min_withdrawal','20.00','float','Minimum withdrawal amount','financial',1,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(5,'max_withdrawal_daily','1000.00','float','Maximum daily withdrawal','financial',1,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(6,'negative_balance_trigger','-50.00','float','Balance threshold to trigger negative balance','financial',0,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(7,'task_expiry_hours','24','integer','Hours before task expires','tasks',0,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(8,'max_tasks_per_day','10','integer','Maximum tasks per day for VIP 1','tasks',0,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(9,'referral_commission_rate','5.00','float','Referral commission percentage','referral',0,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(10,'maintenance_mode','false','boolean','Enable maintenance mode','system',0,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(11,'registration_enabled','true','boolean','Enable user registration','system',1,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(12,'welcome_popup_text','Welcome to Bamboo! During our anniversary celebration, new users can get a bonus for the first time to complete group tasks.','string','Welcome popup message','general',1,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09'),(13,'notification_banner','Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!','string','Main notification banner','general',1,NULL,'2025-06-27 08:23:09','2025-06-27 08:23:09');
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tasks`
--

DROP TABLE IF EXISTS `tasks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tasks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_earned` decimal(10,2) NOT NULL DEFAULT '0.00',
  `base_commission` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vip_bonus` decimal(10,2) NOT NULL DEFAULT '0.00',
  `status` enum('assigned','in_progress','completed','failed','expired') NOT NULL DEFAULT 'assigned',
  `assigned_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `submission_data` json DEFAULT NULL,
  `admin_notes` text,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_product_id` (`product_id`),
  KEY `idx_status` (`status`),
  KEY `idx_assigned_at` (`assigned_at`),
  KEY `idx_completed_at` (`completed_at`),
  KEY `idx_task_user_status` (`user_id`,`status`),
  CONSTRAINT `tasks_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `tasks_ibfk_2` FOREIGN KEY (`product_id`) REFERENCES `products` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tasks`
--

LOCK TABLES `tasks` WRITE;
/*!40000 ALTER TABLE `tasks` DISABLE KEYS */;
INSERT INTO `tasks` VALUES (3,4,1,50.00,5.00,5.00,0.00,'completed','2025-06-27 08:50:21','2025-06-27 08:50:21','2025-06-27 08:50:21','2025-06-28 08:50:21',NULL,'Task completed by Alice'),(4,5,2,80.00,8.00,8.00,0.00,'completed','2025-06-27 08:50:21','2025-06-27 08:50:21','2025-06-27 08:50:21','2025-06-28 08:50:21',NULL,'Task completed by Bob'),(5,4,1,0.00,49.95,49.95,0.00,'assigned','2025-06-27 09:13:09',NULL,NULL,'2025-06-28 09:13:09',NULL,NULL);
/*!40000 ALTER TABLE `tasks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `transactions`
--

DROP TABLE IF EXISTS `transactions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `transactions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `type` enum('deposit','withdrawal','commission','bonus','referral_bonus','penalty','adjustment') NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `balance_before` decimal(10,2) NOT NULL,
  `balance_after` decimal(10,2) NOT NULL,
  `status` enum('pending','processing','completed','failed','cancelled') NOT NULL DEFAULT 'pending',
  `payment_method` varchar(50) DEFAULT NULL,
  `transaction_id` varchar(100) DEFAULT NULL,
  `external_transaction_id` varchar(255) DEFAULT NULL,
  `fee_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `description` text,
  `admin_notes` text,
  `processed_by` int(11) DEFAULT NULL,
  `processed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `transaction_id` (`transaction_id`),
  KEY `processed_by` (`processed_by`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_status` (`status`),
  KEY `idx_transaction_id` (`transaction_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_transaction_user_type` (`user_id`,`type`),
  CONSTRAINT `transactions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `transactions_ibfk_2` FOREIGN KEY (`processed_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `transactions`
--

LOCK TABLES `transactions` WRITE;
/*!40000 ALTER TABLE `transactions` DISABLE KEYS */;
INSERT INTO `transactions` VALUES (1,4,'deposit',200.00,0.00,200.00,'completed','bank','TXN000001',NULL,0.00,NULL,NULL,NULL,NULL,'2025-06-27 08:50:21','2025-06-27 08:50:21'),(2,5,'deposit',300.00,0.00,300.00,'completed','bank','TXN000002',NULL,0.00,NULL,NULL,NULL,NULL,'2025-06-27 08:50:21','2025-06-27 08:50:21'),(3,4,'withdrawal',50.00,200.00,150.00,'completed','bank','TXN000003',NULL,0.00,NULL,NULL,NULL,NULL,'2025-06-27 08:50:21','2025-06-27 08:50:21'),(4,5,'withdrawal',100.00,300.00,200.00,'completed','bank','TXN000004',NULL,0.00,NULL,NULL,NULL,NULL,'2025-06-27 08:50:21','2025-06-27 08:50:21'),(5,4,'adjustment',40000.00,100.00,40100.00,'completed',NULL,NULL,NULL,0.00,'Admin balance adjustment (addition)',NULL,NULL,NULL,'2025-06-27 09:20:22','2025-06-27 09:20:22'),(6,4,'adjustment',20000.00,40100.00,20100.00,'completed',NULL,NULL,NULL,0.00,'Admin balance adjustment (deduction)',NULL,NULL,NULL,'2025-06-27 09:21:30','2025-06-27 09:21:30'),(7,4,'adjustment',1223333.00,20100.00,1243433.00,'completed',NULL,NULL,NULL,0.00,'Admin balance adjustment (addition)',NULL,NULL,NULL,'2025-06-27 09:27:56','2025-06-27 09:27:56'),(8,4,'adjustment',10000.00,1243433.00,1233433.00,'completed',NULL,NULL,NULL,0.00,'Admin balance adjustment (deduction)',NULL,NULL,NULL,'2025-06-27 09:32:41','2025-06-27 09:32:41');
/*!40000 ALTER TABLE `transactions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Temporary table structure for view `user_dashboard_view`
--

DROP TABLE IF EXISTS `user_dashboard_view`;
/*!50001 DROP VIEW IF EXISTS `user_dashboard_view`*/;
SET @saved_cs_client     = @@character_set_client;
SET character_set_client = utf8;
/*!50001 CREATE VIEW `user_dashboard_view` AS SELECT 
 1 AS `id`,
 1 AS `username`,
 1 AS `balance`,
 1 AS `commission_balance`,
 1 AS `vip_level`,
 1 AS `vip_name`,
 1 AS `max_daily_tasks`,
 1 AS `tasks_completed_today`,
 1 AS `referral_count`,
 1 AS `total_commission_earned`,
 1 AS `pending_tasks`*/;
SET character_set_client = @saved_cs_client;

--
-- Table structure for table `user_salaries`
--

DROP TABLE IF EXISTS `user_salaries`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_salaries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` decimal(15,2) NOT NULL,
  `status` enum('paid','pending_approval') NOT NULL,
  `admin_id_processed` int(11) NOT NULL,
  `paid_at` datetime DEFAULT NULL,
  `notes` text,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_salaries`
--

LOCK TABLES `user_salaries` WRITE;
/*!40000 ALTER TABLE `user_salaries` DISABLE KEYS */;
INSERT INTO `user_salaries` VALUES (1,1,1000.00,'paid',1,NULL,'','2025-06-27 06:21:47'),(2,1,5000.00,'paid',1,NULL,'','2025-06-27 07:17:04');
/*!40000 ALTER TABLE `user_salaries` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_sessions`
--

DROP TABLE IF EXISTS `user_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `user_sessions` (
  `id` varchar(128) NOT NULL,
  `user_id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text NOT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_last_activity` (`last_activity`),
  CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_sessions`
--

LOCK TABLES `user_sessions` WRITE;
/*!40000 ALTER TABLE `user_sessions` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL,
  `phone` varchar(20) NOT NULL,
  `usdt_wallet_address` varchar(255) DEFAULT NULL,
  `exchange_name` varchar(100) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `password_hash` varchar(255) NOT NULL,
  `withdrawal_pin_hash` varchar(255) NOT NULL,
  `gender` enum('male','female') NOT NULL,
  `invitation_code` varchar(20) NOT NULL,
  `invited_by` int(11) DEFAULT NULL,
  `balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `frozen_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_deposited` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_withdrawn` decimal(10,2) NOT NULL DEFAULT '0.00',
  `total_commission_earned` decimal(10,2) NOT NULL DEFAULT '0.00',
  `vip_level` int(11) NOT NULL DEFAULT '1',
  `tasks_completed_today` int(11) NOT NULL DEFAULT '0',
  `last_task_date` date DEFAULT NULL,
  `status` enum('pending','active','suspended','banned') NOT NULL DEFAULT 'pending',
  `email_verified` tinyint(1) NOT NULL DEFAULT '0',
  `phone_verified` tinyint(1) NOT NULL DEFAULT '0',
  `avatar_url` varchar(500) DEFAULT NULL,
  `referral_count` int(11) NOT NULL DEFAULT '0',
  `last_login` timestamp NULL DEFAULT NULL,
  `login_count` int(11) NOT NULL DEFAULT '0',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `phone` (`phone`),
  UNIQUE KEY `invitation_code` (`invitation_code`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_username` (`username`),
  KEY `idx_phone` (`phone`),
  KEY `idx_email` (`email`),
  KEY `idx_invitation_code` (`invitation_code`),
  KEY `idx_invited_by` (`invited_by`),
  KEY `idx_vip_level` (`vip_level`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_user_vip_status` (`vip_level`,`status`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`vip_level`) REFERENCES `vip_levels` (`level`) ON UPDATE CASCADE,
  CONSTRAINT `users_ibfk_2` FOREIGN KEY (`invited_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'remi900','10010101010','11','11','<EMAIL>','$2y$10$UHHM.OvmC8OCqfyBQtDqTO4FXvTx2UIW.3rNHDZ7ic0BTrh72dsKe','$2y$10$BRPOND1n56lDO/m2aWvrOOoHrJf8WCBkf8SBembVmX3IVLpbhyHKK','male','EFB0C02A',NULL,1000.00,0.00,0.00,0.00,0.00,0.00,2,0,'2025-06-27','active',0,0,NULL,0,NULL,0,'2025-06-27 06:36:45','2025-06-27 10:39:22'),(4,'alice','1234567890','111111111111111111111111111','111111111111111111','<EMAIL>','$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi','$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi','female','ALICE01',NULL,1233433.00,10.00,0.00,200.00,50.00,20.00,1,3,'2025-06-27','active',1,1,NULL,0,'2025-06-27 08:49:47',5,'2025-06-27 08:49:47','2025-06-27 09:32:41'),(5,'bob','2345678901',NULL,NULL,'<EMAIL>','$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi','$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi','male','BOB01',1,150.00,15.00,0.00,300.00,100.00,30.00,2,1,'2025-06-27','active',1,1,NULL,1,'2025-06-27 08:49:47',10,'2025-06-27 08:49:47','2025-06-27 08:49:47');
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `vip_levels`
--

DROP TABLE IF EXISTS `vip_levels`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `vip_levels` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `level` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `min_balance` decimal(10,2) NOT NULL DEFAULT '0.00',
  `max_daily_tasks` int(11) NOT NULL DEFAULT '10',
  `commission_multiplier` decimal(3,2) NOT NULL DEFAULT '1.00',
  `withdrawal_limit_daily` decimal(10,2) NOT NULL DEFAULT '1000.00',
  `withdrawal_fee_percentage` decimal(5,2) NOT NULL DEFAULT '2.00',
  `benefits` text,
  `icon_path` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `level` (`level`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `vip_levels`
--

LOCK TABLES `vip_levels` WRITE;
/*!40000 ALTER TABLE `vip_levels` DISABLE KEYS */;
INSERT INTO `vip_levels` VALUES (1,1,'VIP 1',0.00,5,1.00,100.00,5.00,'Basic access to platform','685e60c79e5a0.png','2025-06-27 08:23:08','2025-06-27 09:13:43'),(2,2,'VIP 2',100.00,10,1.20,500.00,4.00,'Unlimited access to all features',NULL,'2025-06-27 08:23:08','2025-06-27 08:23:08'),(3,3,'VIP 3',500.00,15,1.50,1000.00,3.00,'Premium features and higher commissions',NULL,'2025-06-27 08:23:08','2025-06-27 08:23:08'),(4,4,'VIP 4',1000.00,20,1.80,2000.00,2.50,'Elite status with maximum benefits',NULL,'2025-06-27 08:23:08','2025-06-27 08:23:08'),(5,5,'VIP 5',2500.00,30,2.00,5000.00,2.00,'Ultimate VIP with highest rewards',NULL,'2025-06-27 08:23:08','2025-06-27 08:23:08');
/*!40000 ALTER TABLE `vip_levels` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `withdrawal_quotes`
--

DROP TABLE IF EXISTS `withdrawal_quotes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `withdrawal_quotes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `status` enum('active','resolved') NOT NULL DEFAULT 'active',
  `admin_id_created` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `withdrawal_quotes`
--

LOCK TABLES `withdrawal_quotes` WRITE;
/*!40000 ALTER TABLE `withdrawal_quotes` DISABLE KEYS */;
/*!40000 ALTER TABLE `withdrawal_quotes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Final view structure for view `admin_user_stats`
--

/*!50001 DROP VIEW IF EXISTS `admin_user_stats`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = cp850 */;
/*!50001 SET character_set_results     = cp850 */;
/*!50001 SET collation_connection      = cp850_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `admin_user_stats` AS select count(0) AS `total_users`,count((case when (`users`.`status` = 'active') then 1 end)) AS `active_users`,count((case when (cast(`users`.`created_at` as date) = curdate()) then 1 end)) AS `new_today`,sum(`users`.`balance`) AS `total_balance`,sum(`users`.`total_deposited`) AS `total_deposits`,sum(`users`.`total_withdrawn`) AS `total_withdrawals` from `users` */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `user_dashboard_view`
--

/*!50001 DROP VIEW IF EXISTS `user_dashboard_view`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = cp850 */;
/*!50001 SET character_set_results     = cp850 */;
/*!50001 SET collation_connection      = cp850_general_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`root`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `user_dashboard_view` AS select `u`.`id` AS `id`,`u`.`username` AS `username`,`u`.`balance` AS `balance`,`u`.`commission_balance` AS `commission_balance`,`u`.`vip_level` AS `vip_level`,`vl`.`name` AS `vip_name`,`vl`.`max_daily_tasks` AS `max_daily_tasks`,`u`.`tasks_completed_today` AS `tasks_completed_today`,`u`.`referral_count` AS `referral_count`,`u`.`total_commission_earned` AS `total_commission_earned`,(select count(0) from `tasks` `t` where ((`t`.`user_id` = `u`.`id`) and (`t`.`status` = 'assigned'))) AS `pending_tasks` from (`users` `u` join `vip_levels` `vl` on((`u`.`vip_level` = `vl`.`level`))) where (`u`.`status` = 'active') */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-27 13:52:19
