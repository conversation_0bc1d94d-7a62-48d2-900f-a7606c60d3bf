<?php
/**
 * Bamboo Web Application - Delete Negative Setting API
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$setting_id = (int)($_POST['setting_id'] ?? 0);

if ($setting_id <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid setting ID.'], 400);
}

try {
    $delete_success = deleteRecord('negative_settings', 'id = ?', [$setting_id]);

    if ($delete_success) {
        jsonResponse(['success' => true, 'message' => 'Negative setting deleted successfully!']);
    } else {
        jsonResponse(['success' => false, 'message' => 'Failed to delete negative setting.'], 500);
    }

} catch (Exception $e) {
    logError('Error deleting negative setting: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>