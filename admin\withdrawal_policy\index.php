<?php
/**
 * Bamboo Web Application - Withdrawal Policy
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $settings_to_update = [
            'withdrawal_policy_name' => sanitizeInput($_POST['withdrawal_policy_name'] ?? ''),
            'min_withdrawal_amount' => (float)($_POST['min_withdrawal_amount'] ?? 0),
            'withdrawal_policy_text' => sanitizeInput($_POST['withdrawal_policy_text'] ?? ''),
        ];

        foreach ($settings_to_update as $key => $value) {
            updateSetting($key, $value);
        }

        showSuccess('Withdrawal policy updated successfully!');
        redirect('admin/withdrawal_policy/');
        exit();
    }
}

// Get current settings
$current_settings = [
    'withdrawal_policy_name' => getAppSetting('withdrawal_policy_name', 'Withdraw Policy'),
    'min_withdrawal_amount' => getAppSetting('min_withdrawal_amount', 100),
    'withdrawal_policy_text' => getAppSetting('withdrawal_policy_text', '')
];

$page_title = 'Withdraw Policy';
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <h1 class="h3 mb-4">Withdraw Policy</h1>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bank me-2"></i>Edit Withdraw Policy
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="withdrawal_policy_name" class="form-label">Policy Name</label>
                                    <input type="text" class="form-control" id="withdrawal_policy_name" name="withdrawal_policy_name"
                                           value="<?php echo htmlspecialchars($current_settings['withdrawal_policy_name']); ?>" required>
                                    <div class="form-text">The name/title of the withdrawal policy</div>
                                </div>

                                <div class="col-md-6 mb-3">
                                    <label for="min_withdrawal_amount" class="form-label">The minimum withdrawal amount is</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><?php echo getAppSetting('default_currency', DEFAULT_CURRENCY); ?></span>
                                        <input type="number" class="form-control" id="min_withdrawal_amount" name="min_withdrawal_amount"
                                               value="<?php echo htmlspecialchars($current_settings['min_withdrawal_amount']); ?>"
                                               step="0.01" min="0" required>
                                    </div>
                                    <div class="form-text">Policy amount - minimum amount users can withdraw</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="withdrawal_policy_text" class="form-label">Policy Details</label>
                                <textarea class="form-control" id="withdrawal_policy_text" name="withdrawal_policy_text" rows="10"><?php echo htmlspecialchars($current_settings['withdrawal_policy_text']); ?></textarea>
                                <div class="form-text">Detailed withdrawal policy text that will be displayed to users</div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>Save Changes
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Reset
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
