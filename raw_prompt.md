the website is webased app , pc version and mobile app , the pc version must look good and mobile verson must look like an app , 


the app is where admin add product and user create account to promote the product randomy according to the user profile balance , 
user can register account to get to the platform with invitation code to registration form , the invitation is to know the agent that invited the user ,  ,Register
Back to Login
Username
Type here
Phone No.
Type here
Withdrawal PIN
Type here
Login Password
Type here
Gender

Male
Invitation Code
Type here

Agree with Term&Condition submit 

when user registered and cofrom there account , they can now login , when the user lgin , they will see pop up shwing the site logo , USDT X I the x can be assign from admin , but the idea is the pop up with logo and USDt X , and text sayng , during the anviverary celebration , News users can get a bonous for the firsty time to complete group 

there will be notification banner , which admin can control as well , Welcome to Bamboo, if any assistance, please seek help from our customer service. Have a nice day!
 , welcome the user , then showing this menu and the different type f level , showing the user current level , 
Downline Team


Certificate


Withdraw


Deposit


Terms &
Conditions


Latest
Campaign


FAQ


About
Us

Vip Level

View More






Vip 2 users receive unlimited access to all features of the platform.
● Deposit in accordance with our renewal event
● Profit of 1.0% per task-45 Optimize products per set
● Better Profit and permission
● Up to 135 tasks per day can be submitted by Vip 2 users
● Full access to all other Premium features


--
the user need to deposit to use the account , in the user profile page there iwll be avatar , username  , Credit Score

100
Referral code
H0JWhE

USDT 0.00
Total Profit
USDT 1,060.00
Total Balance
Transactions


Deposit

Salary

withdraw

Records
Personal Information


Edit

Withdrawal Information
Contact Us


Contact Us

Notifications
Logout.

user records , the record show how many task the user have completed All
Pending
Completed
In progress

-----------\

the most important idea is that the app is job offer can of system where we give user task , with there balance for example 1000 , the user come to the submission page , Starting
Remi9000

Today Profit
USDT 0.00
Daily Earnings are automatically Updated

Today Balance
USDT 1,060.00
Each profit earned will be shown and added to the total , 

the user will see 9 product showing only there picture , thwen the user click on start matching the system randomly pick one of those product and show it to the user , with the price, total amount , totoal profit , and the product creation tiome and Appraisals No, submt , when the user click on the start matching the amount of the current product was deducted to show the product , meaning if the user account was empty he wouldn't have been able to click the start matching unless they have money in your profile , when the user click submit , the amount spent and total profit will be refunded back to he user , show the user account abalance and profit   ----------- each user get 1/x matchingmaking of product, the admin can set that 0/12 or 0/45  or 0 to inifntie , the user can do that until the 45 finish daily , user can check record to see how many matching he has submitted , and note if user doesn't submit or click on close they cant match make, they have to complet the submission to get back there money and intrest , the user can continue to click match making and submit to et back money and interest but there is a twitst , called  Negative Settings nevergetive setting show the selected 
Username
x
Wallet balance
x
Order Progress (Completed / Total Quantity)
1/45  this is the total number of task the user taken , 

Negative Setting
trigger number	Product	Amount	Add Settings
trigger numbr means at which number 2/45 when the task get here , the product i selected will show up , with the high amount more than the user account balance that will give the user negative balance ,  and the user must deposit money through the user deposit to continue , but when the user deposit the money in his acvcount , the user can now submit the order to get back his money and the interest , and maybe later admin change the trigger to 44/45 or x/x then user will go therough this process again , user can depoit money ionto there wallet and also withdraw money from the wallet which is the acocunt , admin will approve or decline , 

admin can control everything on the website it’s a web based app , write me highly detailed prompt , using html , css , js , php ,, each page

we using mamp
Local host database name: matchmaking 
Username : root
Password: root


Well organized database sql
Each page on the website or admin need to be on his own folder , html and  php file together ,
Meaning dashboard.php , dashboard.css , dashboard .js

We doing this so it’s easy to edit , delete and modify
All test files must go to test folder
No code should be more than 1000 lines
Error report is importsnt
Error logs ,

The admin must have appearance setting , logo , fav icon ,settings , smtp for notification and share note ,
Security setting 2fa and otp settings

Member management , 
Add user , edit user , manage user , delete user , view user , 

Avatar	member Information	Level	Wallet Information	Today Task	account Status	Registration Time	Last online time	actions
this is for the table header 

ID: l7N4Bg
Phone: **********
Username: djinakonde1
Vip 4	
Wallet balance: $ 26,258.66

freeze balance: $ 0.00- this demo 

55/55	active	2025-06-22, 8:34	2025-06-23, 8:30	
Plus Deduction Negative settings Withdraw Quote payment card Basic information Credit score Salary
Reset task Change login password Modify Payment password My Teem Disable account delete account

i will break down each of the option here ,Plus Deduction

Member phone number

member Nickname

Operate
Select one
increase amount 
decrease amount 


Amount 
-----------------------------------

Negative Settings
Username
djinakonde1
Wallet balance
26,258.66
Order Progress (Completed / Total Quantity)
55/55
Negative Setting
trigger number	Product	Amount	Add Settings
Save DataClose
S/N	Trigger number	amount	status	paid	Creation Date	Actions
No Record found

Negative Setting
trigger number	Product	Amount	Add Settings
Enter trigger number

Select Product
Enter negative amount
Delete settings

------------------------------


Pc version is good but mobile version must look like a app with footer icon menu ,
Support from both user and admin
Bootstrap 5 or higher
Php 8 upward
Hostinger shared hosting
Company name: Notepadsly



as the user come to your platform 

you can make product go viral 

admin go create iuvitation code 

register account with invite 
approve account 


send the ref to admin 


management 

user management .. add user .. ref code .. 

credit user 1000 


dd Product

Product Name
Enter Product Name
Product Level

Select Level
Product image
preview
Product Amount

-------------------------------------------------------

user need money to unlock the account , minium of 100 as v vip 

Membership Level
Add Membership Level
Grade	Name	Price	task	interest (rate%)	actions
1	Normal users	$ 50.00	40	0.50	Revise
2	Vip 2	$ 1500.00	45	1.00	Revise
3	Vip 3	$ 5000.00	50	1.50	Revise
4	Vip 4	$ 10000.00	55	2.00	Revise
5	Premium	$ 20000.00	60	10.00	Revise

-----------------------------------------------------------------------

Add Levels

Level Name
Enter Level Name
Level Percentage
Enter task Interest (rate%)
Level Minimun Amount
Enter Amount
Level task
Enter Number of Task
Level Icons
No file chosen
Vip Introduction(Quote)


 according to the user deposit the badbge go dey show , vip ro premium 


----------------------------------------------------------------------------------------------------

add product 
Add Product

Product Name
Enter Product Name
Product Level

Vip 3
Product image
preview
Product Amount


------------------------------------------------------------------------

Withdrawal Quotes
Username
djinakonde1
Wallet balance
26,258.66
Order Progress (Completed / Total Quantity)
55/55
Add Quote
message	status	Creation Date	Actions
Caution: Unable to apply for withdrawal normally, please contact customer service to handle your withdrawal fee.	active	2025-06-22, 8:48	MoreDelete
--------------------------------
Payment card

Member phone number
**********
member Wallet Address
******************************************
Exchange
DJINABA konde
SubmitClose
---------------------
Basic information

Membership Level

Vip 4
Member phone number
**********
member Username
djinakonde1

Superior Information
Superior phone
6378383993933
Superior username
TEKNIZZAL
Superior id
--------------------
Salary Settings
Pay Salary
Username
djinakonde1
Wallet balance
26,258.66
Salary History

amount	status	Creation Date	Actions
No Record found
---------------

--------------------
transactions


Deposit

Salary

withdraw

Records
Personal Information


Edit

Withdrawal Information
Contact Us


Contact Us

|withdrawl billing /./.


the app generak setting 
basic Information
App Configruation
App name
Bamboo
App Logo (click to add Certificate)

App Certificate (click to add Certificate)

opening hours (0hour to 23hours)
23
closing hour (0hour to 23hours)
11
sign up bonus
0.00
Receiving orders limit the minimum wallet balance
45
contract terms
Contract Rules

1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of  100 USDT. 

1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.

2) User withdrawals and system withdrawal requirements / security of user funds

2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements

2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.

2.3) All users are not allowed to apply for withdrawal in the middle of task to avoid affecting the merchant's operation

2.4) Users' funds are completely safe on the Platform and the Platform will be liable for any accidental loss

(3) Please do not disclose your account password and withdrawal password to others. The platform will not be held responsible for any loss or damage caused.

3.1) All users are advised to keep their accounts secure to avoid disclosure 

(3.2) The Platform is not responsible for any accidental disclosure of accounts

3.3) Because of the financial implications of the accounts, it is important not to disclose them to avoid unnecessary problems.

3.4) Withdrawal password It is recommended that you do not set a birthday password, ID card number or mobile phone number, etc. It is recommended that you set a more difficult password to protect your funds.

3.5) If you forget your password, you can reset it by contacting the online service and be sure to change it yourself afterwards.

(4) Optimization rating are randomly assigned by the system and therefore cannot be changed, canceled, controlled or skipped in any way

4.1) Due to a large number of users on the platform, it is not possible to distribute group purchase items manually, so all group purchase items are distributed randomly by the system.

(4.2) Group purchase/combination items are randomly released by the system and cannot be changed/cancelled/skipped by any user/staff.

5. Legal action will be taken in the event of misuse of the account

6. Each item comes from a different merchant, no deposit for more than 10 minutes, and each deposit must be made with the online service to confirm the merchant's cryptocurrency wallet address

7. The platform will not be held responsible for any deposits made to the wrong wallet address

8. Each time optimization rating, the user is required to complete it within 2   hours, if it is not completed without notifying the merchant to apply for a time extension, resulting in complaints from the merchant, the user is liable for breach of contract



about Us
ABOUT US

Bamboo has been a trusted partner in the truest sense of the word.



We approach growth from multiple angles.



Most agencies only focus on channel management to determine success. This is no longer enough. Bamboo looks beyond the channels to understand, forecast, and make smarter investments over time

Frequently Asked Questions
I. Start Product Optimization Task 
1.1) Minimum account balance of 100 USDT for the first 40 tasks/set 
1.2) A minimum renewal of 100 USDT is required to start the tasks
1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.

II. Withdrawal
2.1) Full withdrawal amount can be requested after completing 1 group of task
2.2) Users need to complete 1 group of tasks before they can request a withdrawal.
2.3) You cannot request a withdrawal or refund if you choose to give up or withdraw in the middle of a task optimization. 
2.4) No withdrawals can be processed if the user's withdrawal request has not been received.
2.5) If the withdrawal amount is 30000 USDT or above, please contact our online customer service to make a withdrawal. 

III. Funds
3.1) All funds will be held securely in the user's account and can be requested in full once all data has been completed
3.2) To avoid any loss of funds, all data will be processed by the system and not manually
3.3) The platform will take full responsibility for any accidental loss of funds.
3.4) If the user's funds exceed the taxable amount of the UK government, the user will need to pay tax

IV. Account Security
(4.1) Please do not disclose your login password and withdrawal password to others, as the platform will not be held responsible for any loss.
(4.2) Users are not recommended to set their birthday password, ID card number, or mobile phone number as their withdrawal password or login password.
4.3) If you forget your login password or withdrawal password, please contact our online service to reset it.

V. General Product Data
5.1) Platform earnings are divided into normal earnings and  quintuple earnings
5.2) Normal users will earn 0.5% of the profit for each normal task of optimizing
5.3) VIP 2 users will earn 1% of the profit for each normal task of optimizing
5.4) VIP 3 users will earn 1.5% of the profit for each normal task of optimizing
5.5) VIP 4 users will earn 2% of the profit for each normal task of optimizing
5.6) Funds and earnings will be refunded to the user's account for each completed task of optimizing
5.7) The system will randomly allocate tasks to the user's account based on the total amount in the user's account
5.8) Once the tasks of optimise product have been allocated to the user's account they cannot be cancelled or skipped

VI. Combined Product Data
6.1) Combined Product Data is composed of 0 to 3 data, the user may not necessarily get 3 products, the system will randomly allocate the data in the combined product the user has a higher chance of getting 1 or 2 product
6.2) Users will receive five times the profit for each product data combined  than for the general product data
6.3) Once you have received the combined product data, all funds will stop rolling and will be returned to your account after you have completed each product data in the combined.
6.4) The system will randomly allocate the combined product data to the user's account according to the total balance on the user's account
6.5) Once the combined product data has been allocated to the user's account, it cannot be cancelled or skipped.

VII. Deposit
(7.1) The amount of the deposit is the choice of the user, we cannot decide the amount of the deposit for the user, we suggest that the user can make the advance according to his ability or after he is familiar with the platform.
(7.2) If a user needs to make a deposit when receiving combined product data, it is recommended that the user be able to make an advance based on the insufficient amount shown on the account.
7.3) Before making an advance, you must request an advance from the online service and confirm the deposit detail.
7.4) The platform will not be held responsible if the user deposits the wrong deposit detail.

VIII. Merchant Cooperation
(8.1) There are different products on the platform at each time, if the product is not optimized for a long period, the merchant will not be able to offload the task, which will affect the merchant product optimization progress, it is recommended that the user completes all product optimization and applies for a withdrawal as soon as possible to avoid affecting the merchant progress
(8.2) The merchant will provide a deposit detail for the user to make a deposit 
(8.3) Any delay in completing the product optimization will be detrimental to the merchant and the process
(8.4) User does not deal with it for a long time, the merchant has the right to complain about the user's account, resulting in a bad reputation for the user's account

IX. Invitation
9.1) Users will be able to invite other users using the invitation code on their account
9.2) If the account has not yet completed all the data, it is not possible to invite other users
9.3) The referrer will receive 20% of the referee's total earnings for the day

X. Operating hours
(10.1) Platform operating hours 9:00 to 21:59
(10.2) Online service operating hours 9:00 to 21:59
(10.3) Platform withdrawal hour 9:00 to 21:59

Latest events
I. Start Product Optimization Task 
1.1) Minimum account balance of 100 USDT for the first 40 tasks/set 
1.2) A minimum renewal of 100USDT is required to start the tasks
1.3) Once one set of tasks has been completed, the user must request a full withdrawal and receive the withdrawal amount before requesting to reset the account.

II. Withdrawal
2.1) Full withdrawal amount can be requested after completing 1 group of task
2.2) Users need to complete 1 group of tasks before they can request a withdrawal.
2.3) You cannot request a withdrawal or refund if you choose to give up or withdraw in the middle of a task optimization. 
2.4) No withdrawals can be processed if the user's withdrawal request has not been received.
2.5) If the withdrawal amount is 30000 USDT or above, please contact our online customer service to make a withdrawal. 

III. Funds
3.1) All funds will be held securely in the user's account and can be requested in full once all data has been completed
3.2) To avoid any loss of funds, all data will be processed by the system and not manually
3.3) The platform will take full responsibility for any accidental loss of funds.
3.4) If the user's funds exceed the taxable amount of the UK government, the user will need to pay tax

IV. Account Security
(4.1) Please do not disclose your login password and withdrawal password to others, as the platform will not be held responsible for any loss.
(4.2) Users are not recommended to set their birthday password, ID card number, or mobile phone number as their withdrawal password or login password.
4.3) If you forget your login password or withdrawal password, please contact our online service to reset it.

V. General Product Data
5.1) Platform earnings are divided into normal earnings and  quintuple earnings
5.2) Normal users will earn 0.5% of the profit for each normal task of optimizing
5.3) VIP 2 users will earn 1% of the profit for each normal task of optimizing
5.4) VIP 3 users will earn 1.5% of the profit for each normal task of optimizing
5.5) VIP 4 users will earn 2% of the profit for each normal task of optimizing
5.6) Funds and earnings will be refunded to the user's account for each completed task of optimizing
5.7) The system will randomly allocate tasks to the user's account based on the total amount in the user's account
5.8) Once the tasks of optimise product have been allocated to the user's account they cannot be cancelled or skipped

VI. Combined Product Data
6.1) Combined Product Data is composed of 0 to 3 data, the user may not necessarily get 3 products, the system will randomly allocate the data in the combined product the user has a higher chance of getting 1 or 2 product
6.2) Users will receive five times the profit for each product data combined  than for the general product data
6.3) Once you have received the combined product data, all funds will stop rolling and will be returned to your account after you have completed each product data in the combined.
6.4) The system will randomly allocate the combined product data to the user's account according to the total balance on the user's account
6.5) Once the combined product data has been allocated to the user's account, it cannot be cancelled or skipped.

VII. Deposit
(7.1) The amount of the deposit is the choice of the user, we cannot decide the amount of the deposit for the user, we suggest that the user can make the advance according to his ability or after he is familiar with the platform.
(7.2) If a user needs to make a deposit when receiving combined product data, it is recommended that the user be able to make an advance based on the insufficient amount shown on the account.
7.3) Before making an advance, you must request an advance from the online service and confirm the deposit detail.
7.4) The platform will not be held responsible if the user deposits the wrong deposit detail.

VIII. Merchant Cooperation
(8.1) There are different products on the platform at each time, if the product is not optimized for a long period, the merchant will not be able to offload the task, which will affect the merchant product optimization progress, it is recommended that the user completes all product optimization and applies for a withdrawal as soon as possible to avoid affecting the merchant progress
(8.2) The merchant will provide a deposit detail for the user to make a deposit 
(8.3) Any delay in completing the product optimization will be detrimental to the merchant and the process
(8.4) User does not deal with it for a long time, the merchant has the right to complain about the user's account, resulting in a bad reputation for the user's account

IX. Invitation
9.1) Users will be able to invite other users using the invitation code on their account
9.2) If the account has not yet completed all the data, it is not possible to invite other users
9.3) The referrer will receive 20% of the referee's total earnings for the day

X. Operating hours
(10.1) Platform operating hours 9:00 to 21:59
(10.2) Online service operating hours 9:00 to 21:59
(10.3) Platform withdrawal hour 9:00 to 21:59

User Registration Agreement
Contract Rules

1) To optimize and reset your account, you must first complete all ratings with a minimum amount of 100 USDT and a minimum account reset amount of  100 USDT. 

1.1) If you need to reset your account, you must contact our online service to reset your account after you have completed all your optimization and withdrawn your funds.

2) User withdrawals and system withdrawal requirements / security of user funds

2.1) Each user needs to complete all the optimization rating before they can meet the system withdrawal requirements

2.2) In order to avoid any loss of funds, all withdrawals are processed automatically by the system and not manually.

2.3) All users are not allowed to apply for withdrawal in the middle of task to avoid affecting the merchant's operation

2.4) Users' funds are completely safe on the Platform and the Platform will be liable for any accidental loss

(3) Please do not disclose your account password and withdrawal password to others. The platform will not be held responsible for any loss or damage caused.

3.1) All users are advised to keep their accounts secure to avoid disclosure 

(3.2) The Platform is not responsible for any accidental disclosure of accounts

3.3) Because of the financial implications of the accounts, it is important not to disclose them to avoid unnecessary problems.

3.4) Withdrawal password It is recommended that you do not set a birthday password, ID card number or mobile phone number, etc. It is recommended that you set a more difficult password to protect your funds.

3.5) If you forget your password, you can reset it by contacting the online service and be sure to change it yourself afterwards.

(4) Optimization rating are randomly assigned by the system and therefore cannot be changed, canceled, controlled or skipped in any way

4.1) Due to a large number of users on the platform, it is not possible to distribute group purchase items manually, so all group purchase items are distributed randomly by the system.

(4.2) Group purchase/combination items are randomly released by the system and cannot be changed/cancelled/skipped by any user/staff.

5. Legal action will be taken in the event of misuse of the account

6. Each item comes from a different merchant, no deposit for more than 10 minutes, and each deposit must be made with the online service to confirm the merchant's cryptocurrency wallet address

7. The platform will not be held responsible for any deposits made to the wrong wallet address

8. Each time optimization rating, the user is required to complete it within 2   hours, if it is not completed without notifying the merchant to apply for a time extension, resulting in complaints from the merchant, the user is liable for breach of contract



-------------------------

product list 

Add Product

Product Name
Enter Product Name
Product Level

Select Level
Product image
preview
Product Amount

S/N	Picture	Title	Product Level	price	Creation Time	actions




dashboard details 

Mail Statistics
Total number of users (persons)

Total number of current users,
-
Total amount of goods (pieces)

The current total quantity of goods,

The total amount of completed orders (order)

Total number of orders submitted,

Completed order amount (dollar)

The total amount of the order submitted,

Recharge order amount (dollar)

Approved top-up orders,

Cash withdrawal order amount (dollar)
Approved withdrawal order,

Total amount issued (dollar)
The total amount of all order revenue and activity rewards of the user,


------------------------------------

Dsitrbution settings,
distribution settings
Level 1 rebates (percentage)
20
Second rebate
20
Third rebate
20

------------------------
Membership Level
Add Membership Level
Grade	Name	Price	task	interest (rate%)	actions
1	Normal users	$ 50.00	40	0.50	Revise
2	Vip 2	$ 1500.00	45	1.00	Revise
3	Vip 3	$ 5000.00	50	1.50	Revise
4	Vip 4	$ 10000.00	55	2.00	Revise
5	Premium	$ 20000.00	60	10.00	Revise

----------------------------

Customer Service List
Add Customer Service
S/N	Name	Link	Creation Time	actions
7	WORLD BAMBOO CUSTOMER SERVICE TG1	https://t.me/BambooCS0	2024-02-28, 10:12	ReviseDelete
14	BAMBOO CUSTOMER SERVICE WHATSAPP	https://wa.me/447828927391	2024-06-20, 4:18	ReviseDelete
17	BAMBOO CUSTOMER SERVICE {BCS}	https://t.me/bamboocustomerservice1	2025-06-10, 9:24	ReviseDelete

--------------------------------------------------------------
Withdraw Policy
Policy name
The minimum withdrawal amount is
Policy amount
100
Save Changes

---------------------

Products List
Add Products
S/N	Picture	Title	Product Level	price	Creation Time	actions
Add Product

Product Name
Enter Product Name
Product Level

Select Level
Normal users		
Vip 2		
Vip 3		
Vip 4		
Premium	

Product image
preview
Product Amount
Enter Amount
SubmitClose

--------------------------------

Traning Account

Username
 Enter Username
Phone number
 Enter Phone number
Login Password
Enter Password
Withdrawal Pin
Enter Withdrawal Pin
Gender

Select gender
Invitation code


