<?php
/**
 * Bamboo Web Application - Admin Header
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get app settings
$app_name = getAppSetting('app_name', APP_NAME);
$app_logo = getAppSetting('app_logo', '');

// Get all appearance settings
$appearance_settings = [
    'primary_color' => getAppSetting('appearance_primary_color', '#ff6900'),
    'secondary_color' => getAppSetting('appearance_secondary_color', '#ffffff'),
    'accent_color' => getAppSetting('appearance_accent_color', '#007bff'),
    'gradient_start' => getAppSetting('appearance_gradient_start', '#ff6900'),
    'gradient_end' => getAppSetting('appearance_gradient_end', '#ff8533'),
    'card_background' => getAppSetting('appearance_card_background', '#ffffff'),
    'sidebar_style' => getAppSetting('appearance_sidebar_style', 'gradient'),
    'card_shadow' => getAppSetting('appearance_card_shadow', 'subtle'),
    'border_radius' => getAppSetting('appearance_border_radius', '0.5rem'),
    'button_style' => getAppSetting('appearance_button_style', 'gradient'),
    'theme_mode' => getAppSetting('appearance_theme_mode', 'light')
];

// Helper function to convert hex to RGB
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return sscanf($hex, "%02x%02x%02x");
}

// Helper function to get shadow value
function getShadowValue($shadowType) {
    switch ($shadowType) {
        case 'none': return 'none';
        case 'subtle': return '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
        case 'medium': return '0 0.5rem 1rem rgba(0, 0, 0, 0.15)';
        case 'strong': return '0 1rem 3rem rgba(0, 0, 0, 0.175)';
        default: return '0 0.125rem 0.25rem rgba(0, 0, 0, 0.075)';
    }
}

// Helper function to get button style
function getButtonStyle($settings) {
    switch ($settings['button_style']) {
        case 'primary':
            return "background: {$settings['primary_color']}";
        case 'accent':
            return "background: {$settings['accent_color']}";
        case 'gradient':
        default:
            return "background: linear-gradient(135deg, {$settings['primary_color']} 0%, {$settings['accent_color']} 100%)";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo htmlspecialchars($page_title ?? $app_name . ' - Admin'); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>images/favicon.ico">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Admin CSS -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/admin.css" rel="stylesheet">

    <!-- Dynamic Theme System -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/dynamic-theme.css" rel="stylesheet">

    <!-- Enhanced Tables Styling -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/enhanced-tables.css" rel="stylesheet">

    <!-- White Theme Override - Ensures all admin pages have white background -->
    <link href="<?php echo BASE_URL; ?>admin/assets/css/admin-white-theme.css" rel="stylesheet">
    
    <!-- Additional CSS files -->
    <?php if (isset($additional_css) && is_array($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link href="<?php echo $css_file; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Dynamic Theme Colors -->
    <style>
        :root {
            /* Primary appearance settings */
            --admin-primary: <?php echo $appearance_settings['primary_color']; ?>;
            --admin-secondary: <?php echo $appearance_settings['secondary_color']; ?>;
            --admin-accent: <?php echo $appearance_settings['accent_color']; ?>;
            --admin-gradient-end: <?php echo $appearance_settings['gradient_end']; ?>;
            --admin-border-radius: <?php echo $appearance_settings['border_radius']; ?>;

            /* RGB values for alpha usage */
            --admin-primary-rgb: <?php echo implode(',', hexToRgb($appearance_settings['primary_color'])); ?>;
            --admin-accent-rgb: <?php echo implode(',', hexToRgb($appearance_settings['accent_color'])); ?>;

            /* Dynamic variables */
            --dynamic-primary: <?php echo $appearance_settings['primary_color']; ?>;
            --dynamic-secondary: <?php echo $appearance_settings['secondary_color']; ?>;
            --dynamic-accent: <?php echo $appearance_settings['accent_color']; ?>;
            --dynamic-gradient-start: <?php echo $appearance_settings['gradient_start']; ?>;
            --dynamic-gradient-end: <?php echo $appearance_settings['gradient_end']; ?>;
            --dynamic-card-bg: <?php echo $appearance_settings['card_background']; ?>;
            --dynamic-border-radius: <?php echo $appearance_settings['border_radius']; ?>;
            --dynamic-primary-rgb: <?php echo implode(',', hexToRgb($appearance_settings['primary_color'])); ?>;
            --dynamic-accent-rgb: <?php echo implode(',', hexToRgb($appearance_settings['accent_color'])); ?>;
            --dynamic-card-shadow: <?php echo getShadowValue($appearance_settings['card_shadow']); ?>;
        }

        /* Apply sidebar style */
        .admin-sidebar {
            <?php if ($appearance_settings['sidebar_style'] === 'gradient'): ?>
                background: linear-gradient(180deg, <?php echo $appearance_settings['gradient_start']; ?> 0%, <?php echo $appearance_settings['gradient_end']; ?> 100%) !important;
            <?php elseif ($appearance_settings['sidebar_style'] === 'solid'): ?>
                background: <?php echo $appearance_settings['primary_color']; ?> !important;
            <?php else: ?>
                background: #f8f9fa !important;
                color: #495057 !important;
            <?php endif; ?>
        }

        /* Apply button style */
        .btn-primary {
            <?php echo getButtonStyle($appearance_settings); ?> !important;
            border: none !important;
            color: white !important;
        }

        /* Apply card styling */
        .card {
            background: <?php echo $appearance_settings['card_background']; ?> !important;
            border-radius: <?php echo $appearance_settings['border_radius']; ?> !important;
            box-shadow: <?php echo getShadowValue($appearance_settings['card_shadow']); ?> !important;
        }
    </style>
    
    <!-- Meta tags -->
    <meta name="robots" content="noindex, nofollow">
    <meta name="theme-color" content="<?php echo $appearance_settings['primary_color']; ?>">
    
    <!-- CSRF Token -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
</head>
<body class="<?php echo $body_class ?? 'admin-page'; ?>">
    
    <!-- Loading Spinner -->
    <div id="admin-loading" class="admin-loading">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    
    <!-- Flash Messages -->
    <?php $flash_messages = getFlashMessages(); ?>
    <?php if (!empty($flash_messages)): ?>
        <div class="admin-flash-messages">
            <?php if (isset($flash_messages['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($flash_messages['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>