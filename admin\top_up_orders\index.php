<?php
/**
 * Bamboo Web Application - Admin Top-Up Orders Page
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';
require_once '../../includes/database.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$page_title = 'Top-Up Orders';
$page_section = 'top_up_orders';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css',
    BASE_URL . 'admin/top_up_orders/top-up-orders.css'
];

// Handle search and pagination with defensive programming
$search_username = isset($_GET['search_username']) ? trim((string)$_GET['search_username']) : '';
$page_param = $_GET['page'] ?? 1;

// Robust page validation
if (!is_numeric($page_param) || $page_param < 1) {
    $page = 1;
} else {
    $page = (int)$page_param;
}

// Ensure variables are properly typed with validation
$search_username = (string)$search_username;
$page = max(1, (int)$page); // Ensure page is at least 1
$per_page = 10;
$offset = ($page - 1) * $per_page;
$where = "WHERE (t.type IN ('admin_credit', 'deposit', 'adjustment') OR t.description LIKE '%admin%')";
$params = [];
if ($search_username !== '') {
    $where .= " AND u.username LIKE ?";
    $params[] = "%$search_username%";
}
$count_query = "SELECT COUNT(*) FROM transactions t JOIN users u ON t.user_id = u.id $where";
$db = getDB();
$stmt = $db->prepare($count_query);
$stmt->execute($params);
$total_orders = (int)$stmt->fetchColumn(); // Ensure integer
$total_pages = (int)ceil($total_orders / $per_page);

// Validate page doesn't exceed total pages
if ($page > $total_pages && $total_pages > 0) {
    $page = $total_pages;
    $offset = ($page - 1) * $per_page;
}

$query = "SELECT t.*, u.username, u.email, u.phone, u.usdt_wallet_address
          FROM transactions t
          JOIN users u ON t.user_id = u.id
          $where
          ORDER BY t.created_at DESC
          LIMIT $per_page OFFSET $offset";
$top_up_orders = fetchAll($query, $params);

// Final type casting before template to prevent string-int operation errors
$page = (int)$page;
$per_page = (int)$per_page;

include '../includes/admin_header.php';
?>

<style>
/* Enhanced Table Styling with Visible Row Borders */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table tbody tr {
    border-bottom: 1px solid #dee2e6 !important;
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    transform: scale(1.005);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08);
}

.table tbody tr:last-child {
    border-bottom: 1px solid #dee2e6 !important;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6 !important;
    border-top: 1px solid #dee2e6 !important;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table td {
    vertical-align: middle;
    padding: 1rem 0.75rem;
    border-left: none;
    border-right: none;
}

.table th {
    border-left: none;
    border-right: none;
}

/* Serial Number Styling */
.serial-number {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}
</style>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="page-header">
                    <h1><?php echo $page_title; ?></h1>
                    <p class="text-muted">Manage and monitor all top-up orders and transactions</p>
                </div>

                <div class="orders-card card">
                    <div class="card-header d-flex flex-wrap justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-cash-stack me-2"></i>All Top-Up Orders
                        </h5>
                        <form class="search-form" method="get" action="">
                            <input type="text" class="form-control" name="search_username" placeholder="Search by Username" value="<?php echo htmlspecialchars($search_username); ?>">
                            <button class="btn btn-primary" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                        </form>
                    </div>
                    <div class="card-body">
                        <?php if (empty($top_up_orders)): ?>
                            <div class="empty-state">
                                <i class="bi bi-inbox"></i>
                                <h5>No Top-Up Orders Found</h5>
                                <p>There are no top-up orders matching your criteria.</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="modern-table table align-middle">
                                    <thead>
                                        <tr>
                                            <th style="width:40px;">#</th>
                                            <th style="min-width:150px;">Member Info</th>
                                            <th style="min-width:100px;">Order No</th>
                                            <th style="width:90px;">Amount</th>
                                            <th style="width:110px;">Payment</th>
                                            <th style="min-width:100px;">Wallet</th>
                                            <th style="width:70px;">Status</th>
                                            <th style="width:120px;">Created</th>
                                            <th style="min-width:100px;">Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        // Ensure proper numbering calculation
                                        $current_page = max(1, (int)$page);
                                        $items_per_page = max(1, (int)$per_page);
                                        $serial_no = ($current_page - 1) * $items_per_page + 1;
                                        ?>
                                        <?php foreach ($top_up_orders as $order): ?>
                                            <tr>
                                                <td><div class="serial-number"><?php echo $serial_no++; ?></div></td>
                                                <td>
                                                    <div class="member-info">
                                                        <div class="username">U: <?php echo htmlspecialchars(ucfirst($order['username'])); ?></div>
                                                        <div class="contact">
                                                            E: <?php echo htmlspecialchars($order['email']); ?><br>
                                                            P: <?php echo htmlspecialchars($order['phone']); ?>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="order-id"><?php echo htmlspecialchars($order['order_no'] ?? 'N/A'); ?></div>
                                                </td>
                                                <td>
                                                    <div class="order-amount"><?php echo formatCurrency($order['amount']); ?></div>
                                                </td>
                                                <td>
                                                    <div class="payment-method"><?php echo htmlspecialchars($order['payment_method'] ?? $order['payment_channel'] ?? 'Admin Credit'); ?></div>
                                                </td>
                                                <td>
                                                    <div class="wallet-address" title="<?php echo htmlspecialchars($order['usdt_wallet_address'] ?? 'N/A'); ?>">
                                                        <?php echo htmlspecialchars($order['usdt_wallet_address'] ?? 'N/A'); ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="status-badge badge bg-<?php echo getStatusBadgeClass($order['status']); ?>">
                                                        <?php echo ucfirst($order['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="creation-time"><?php echo formatDate($order['created_at']); ?></div>
                                                </td>
                                                <td>
                                                    <div class="order-description" title="<?php echo htmlspecialchars($order['description'] ?? 'N/A'); ?>">
                                                        <?php echo htmlspecialchars($order['description'] ?? 'N/A'); ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <!-- Pagination -->
                            <div class="pagination-enhanced">
                                <nav aria-label="Top-Up Orders Pagination">
                                    <ul class="pagination">
                                        <?php if ($page > 1): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?search_username=<?php echo urlencode($search_username); ?>&page=<?php echo ((int)$page - 1); ?>">
                                                    <i class="bi bi-chevron-left"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>

                                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                            <li class="page-item <?php if ($i == $page) echo 'active'; ?>">
                                                <a class="page-link" href="?search_username=<?php echo urlencode($search_username); ?>&page=<?php echo $i; ?>"><?php echo $i; ?></a>
                                            </li>
                                        <?php endfor; ?>

                                        <?php if ($page < $total_pages): ?>
                                            <li class="page-item">
                                                <a class="page-link" href="?search_username=<?php echo urlencode($search_username); ?>&page=<?php echo ((int)$page + 1); ?>">
                                                    <i class="bi bi-chevron-right"></i>
                                                </a>
                                            </li>
                                        <?php endif; ?>
                                    </ul>
                                </nav>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>
