<?php
/**
 * Bamboo Web Application - User Registration Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('user/dashboard/');
}

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $agree_terms = isset($_POST['agree_terms']);
    
    // Validate CSRF token
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        showError('Invalid security token. Please try again.');
    } else {
        // Validate input
        $errors = [];
        
        if (empty($username)) {
            $errors[] = 'Username is required.';
        } elseif (strlen($username) < 3) {
            $errors[] = 'Username must be at least 3 characters long.';
        } elseif (recordExists('users', 'username = ?', [$username])) {
            $errors[] = 'Username already exists.';
        }
        
        if (empty($email)) {
            $errors[] = 'Email is required.';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Please enter a valid email address.';
        } elseif (recordExists('users', 'email = ?', [$email])) {
            $errors[] = 'Email already registered.';
        }
        
        if (empty($phone)) {
            $errors[] = 'Phone number is required.';
        } elseif (recordExists('users', 'phone = ?', [$phone])) {
            $errors[] = 'Phone number already registered.';
        }
        
        if (empty($password)) {
            $errors[] = 'Password is required.';
        } elseif (strlen($password) < 6) {
            $errors[] = 'Password must be at least 6 characters long.';
        }
        
        if ($password !== $confirm_password) {
            $errors[] = 'Passwords do not match.';
        }
        
        if (!$agree_terms) {
            $errors[] = 'You must agree to the terms and conditions.';
        }
        
        if (empty($errors)) {
            // Create user account
            $user_data = [
                'username' => $username,
                'email' => $email,
                'phone' => $phone,
                'password' => hashPassword($password),
                'status' => 'active',
                'vip_level' => 1,
                'balance' => 0.00,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $user_id = insertRecord('users', $user_data);
            
            if ($user_id) {
                showSuccess('Account created successfully! You can now login.');
                redirect('user/login/');
            } else {
                showError('Failed to create account. Please try again.');
            }
        } else {
            foreach ($errors as $error) {
                showError($error);
                break; // Show only first error
            }
        }
    }
}

// Get app settings safely
$app_name = APP_NAME;
$app_logo = '';
try {
    $app_logo = getAppSetting('app_logo', '');
    $app_name = getAppSetting('app_name', APP_NAME);
} catch (Exception $e) {
    // Fallback if settings table doesn't exist yet
    $app_logo = '';
    $app_name = APP_NAME;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Create Account - <?php echo htmlspecialchars($app_name); ?></title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Meta tags for mobile -->
    <meta name="theme-color" content="#ff6900">
    
    <style>
        body {
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 25%, #ffb366 50%, #ff9f40 75%, #ff6900 100%);
            background-attachment: fixed;
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            box-shadow: 0 40px 80px rgba(0, 0, 0, 0.12);
            padding: 2.5rem 2rem;
            width: 100%;
            max-width: 450px;
            border: 1px solid rgba(255, 255, 255, 0.4);
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .brand-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .brand-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6900, #ff8533);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 2.5rem;
            color: white;
            box-shadow: 0 15px 40px rgba(255, 105, 0, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-8px); }
        }

        .brand-title {
            color: #ff6900;
            font-weight: 800;
            font-size: 2rem;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .welcome-text {
            color: #6c757d;
            font-size: 1rem;
            margin-bottom: 0;
        }

        .form-group {
            margin-bottom: 1.2rem;
            position: relative;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            padding: 1.2rem 1rem 0.8rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background-color: rgba(248, 249, 250, 0.9);
            height: 55px;
            width: 100%;
        }

        .form-control:focus {
            border-color: #ff6900;
            box-shadow: 0 0 0 0.25rem rgba(255, 105, 0, 0.15);
            background-color: white;
            transform: translateY(-2px);
            outline: none;
        }

        .form-control:focus + .form-label,
        .form-control:not(:placeholder-shown) + .form-label {
            transform: translateY(-0.5rem) scale(0.85);
            color: #ff6900;
        }

        .form-label {
            position: absolute;
            top: 1.1rem;
            left: 1rem;
            color: #6c757d;
            font-weight: 500;
            transition: all 0.3s ease;
            pointer-events: none;
            background: transparent;
            z-index: 2;
            font-size: 0.95rem;
        }

        .password-group {
            position: relative;
        }

        .password-group .form-control {
            padding-right: 3.5rem;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            font-size: 1.1rem;
            cursor: pointer;
            z-index: 3;
            transition: all 0.3s ease;
        }

        .password-toggle:hover {
            color: #ff6900;
        }

        .btn-primary {
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
            border: none;
            border-radius: 15px;
            padding: 1rem 2rem;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            width: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(255, 105, 0, 0.4);
        }

        .btn-primary:active {
            transform: translateY(-1px);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-outline-primary {
            border: 2px solid #ff6900;
            color: #ff6900;
            border-radius: 15px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            background: transparent;
            text-decoration: none;
            display: inline-block;
        }

        .btn-outline-primary:hover {
            background: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 105, 0, 0.3);
            text-decoration: none;
        }

        .form-check-input {
            width: 1.2rem;
            height: 1.2rem;
            border: 2px solid #dee2e6;
            border-radius: 6px;
        }

        .form-check-input:checked {
            background-color: #ff6900;
            border-color: #ff6900;
        }

        .form-check-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-left: 0.5rem;
            line-height: 1.4;
        }

        .alert {
            border-radius: 15px;
            border: none;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }

        .alert-success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
        }

        .text-muted {
            color: #6c757d !important;
        }

        .link-primary {
            color: #ff6900 !important;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .link-primary:hover {
            color: #ff8533 !important;
            text-decoration: underline;
        }

        .divider {
            text-align: center;
            margin: 1.5rem 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(to right, transparent, #dee2e6, transparent);
        }

        .divider span {
            background: rgba(255, 255, 255, 0.98);
            padding: 0 1rem;
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* Responsive design */
        @media (max-width: 576px) {
            .register-card {
                padding: 2rem 1.5rem;
                margin: 1rem;
                border-radius: 25px;
            }
            
            .brand-title {
                font-size: 1.8rem;
            }
            
            .brand-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }
            
            .form-group {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <!-- Flash Messages -->
            <?php $flash_messages = getFlashMessages(); ?>
            <?php if (!empty($flash_messages)): ?>
                <?php if (isset($flash_messages['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="bi bi-check-circle-fill me-2"></i>
                        <?php echo htmlspecialchars($flash_messages['success']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($flash_messages['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <?php echo htmlspecialchars($flash_messages['error']); ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Brand Section -->
            <div class="brand-section">
                <?php if ($app_logo && file_exists(UPLOAD_PATH . 'logos/' . $app_logo)): ?>
                    <img src="<?php echo BASE_URL . 'uploads/logos/' . $app_logo; ?>" 
                         alt="<?php echo htmlspecialchars($app_name); ?>" 
                         class="brand-icon img-fluid">
                <?php else: ?>
                    <div class="brand-icon">
                        <i class="bi bi-person-plus"></i>
                    </div>
                <?php endif; ?>
                
                <h1 class="brand-title">Join <?php echo htmlspecialchars($app_name); ?></h1>
                <p class="welcome-text">Create your account to get started</p>
            </div>
            
            <!-- Registration Form -->
            <form method="POST" action="" id="registerForm" novalidate>
                <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                
                <!-- Username Field -->
                <div class="form-group">
                    <input type="text" 
                           class="form-control" 
                           id="username" 
                           name="username" 
                           placeholder=" "
                           value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                           autocomplete="off"
                           autocapitalize="off"
                           spellcheck="false"
                           required>
                    <label for="username" class="form-label">
                        <i class="bi bi-person me-2" style="color: #ff6900;"></i>Username
                    </label>
                </div>
                
                <!-- Email Field -->
                <div class="form-group">
                    <input type="email" 
                           class="form-control" 
                           id="email" 
                           name="email" 
                           placeholder=" "
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                           autocomplete="off"
                           required>
                    <label for="email" class="form-label">
                        <i class="bi bi-envelope me-2" style="color: #ff6900;"></i>Email Address
                    </label>
                </div>
                
                <!-- Phone Field -->
                <div class="form-group">
                    <input type="tel" 
                           class="form-control" 
                           id="phone" 
                           name="phone" 
                           placeholder=" "
                           value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>"
                           autocomplete="off"
                           required>
                    <label for="phone" class="form-label">
                        <i class="bi bi-phone me-2" style="color: #ff6900;"></i>Phone Number
                    </label>
                </div>
                
                <!-- Password Field -->
                <div class="form-group">
                    <div class="password-group">
                        <input type="password" 
                               class="form-control" 
                               id="password" 
                               name="password" 
                               placeholder=" "
                               autocomplete="new-password"
                               required>
                        <button type="button" 
                                class="password-toggle" 
                                id="togglePassword"
                                title="Toggle password visibility">
                            <i class="bi bi-eye"></i>
                        </button>
                        <label for="password" class="form-label">
                            <i class="bi bi-lock me-2" style="color: #ff6900;"></i>Password
                        </label>
                    </div>
                </div>
                
                <!-- Confirm Password Field -->
                <div class="form-group">
                    <div class="password-group">
                        <input type="password" 
                               class="form-control" 
                               id="confirm_password" 
                               name="confirm_password" 
                               placeholder=" "
                               autocomplete="new-password"
                               required>
                        <button type="button" 
                                class="password-toggle" 
                                id="toggleConfirmPassword"
                                title="Toggle password visibility">
                            <i class="bi bi-eye"></i>
                        </button>
                        <label for="confirm_password" class="form-label">
                            <i class="bi bi-lock-fill me-2" style="color: #ff6900;"></i>Confirm Password
                        </label>
                    </div>
                </div>
                
                <!-- Terms Agreement -->
                <div class="form-check mb-3">
                    <input type="checkbox" 
                           class="form-check-input" 
                           id="agree_terms" 
                           name="agree_terms"
                           required>
                    <label class="form-check-label" for="agree_terms">
                        I agree to the <a href="#" class="link-primary">Terms of Service</a> and <a href="#" class="link-primary">Privacy Policy</a>
                    </label>
                </div>
                
                <!-- Register Button -->
                <div class="d-grid mb-3">
                    <button type="submit" 
                            class="btn btn-primary btn-lg"
                            id="registerBtn">
                        <i class="bi bi-person-plus me-2"></i>
                        <span class="btn-text">Create Account</span>
                        <span class="btn-loading d-none">
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            Creating account...
                        </span>
                    </button>
                </div>
                
                <!-- Divider -->
                <div class="divider">
                    <span>Already have an account?</span>
                </div>
                
                <!-- Login Link -->
                <div class="text-center">
                    <a href="<?php echo BASE_URL; ?>user/login/" class="btn-outline-primary">
                        <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Bootstrap 5 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Password toggle functionality
            $('#togglePassword').on('click', function() {
                const passwordField = $('#password');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('bi-eye').addClass('bi-eye-slash');
                    $(this).attr('title', 'Hide password');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('bi-eye-slash').addClass('bi-eye');
                    $(this).attr('title', 'Show password');
                }
            });
            
            // Confirm password toggle functionality
            $('#toggleConfirmPassword').on('click', function() {
                const passwordField = $('#confirm_password');
                const icon = $(this).find('i');
                
                if (passwordField.attr('type') === 'password') {
                    passwordField.attr('type', 'text');
                    icon.removeClass('bi-eye').addClass('bi-eye-slash');
                    $(this).attr('title', 'Hide password');
                } else {
                    passwordField.attr('type', 'password');
                    icon.removeClass('bi-eye-slash').addClass('bi-eye');
                    $(this).attr('title', 'Show password');
                }
            });
            
            // Form submission with loading state
            $('#registerForm').on('submit', function(e) {
                const username = $('#username').val().trim();
                const email = $('#email').val().trim();
                const phone = $('#phone').val().trim();
                const password = $('#password').val();
                const confirmPassword = $('#confirm_password').val();
                const agreeTerms = $('#agree_terms').is(':checked');
                
                // Basic validation
                if (!username || !email || !phone || !password || !confirmPassword) {
                    e.preventDefault();
                    alert('Please fill in all fields.');
                    return false;
                }
                
                if (password !== confirmPassword) {
                    e.preventDefault();
                    alert('Passwords do not match.');
                    return false;
                }
                
                if (!agreeTerms) {
                    e.preventDefault();
                    alert('You must agree to the terms and conditions.');
                    return false;
                }
                
                // Show loading state
                const btn = $('#registerBtn');
                btn.find('.btn-text').addClass('d-none');
                btn.find('.btn-loading').removeClass('d-none');
                btn.prop('disabled', true);
            });
            
            // Auto-hide alerts after 5 seconds
            setTimeout(function() {
                $('.alert').fadeOut();
            }, 5000);
            
            // Focus on username field
            $('#username').focus();
            
            // Clear any browser autofill
            setTimeout(function() {
                $('input[type="text"], input[type="email"], input[type="tel"], input[type="password"]').val('');
            }, 100);
            
            // Disable autofill
            $('input').attr('autocomplete', 'off');
            
            // Real-time password match validation
            $('#confirm_password').on('input', function() {
                const password = $('#password').val();
                const confirmPassword = $(this).val();
                
                if (confirmPassword && password !== confirmPassword) {
                    $(this).css('border-color', '#dc3545');
                } else {
                    $(this).css('border-color', '#e9ecef');
                }
            });
            
            // Handle form autofill detection
            $('input').on('input', function() {
                if ($(this).val() !== '') {
                    $(this).next('.form-label').addClass('active');
                }
            });
        });
    </script>
</body>
</html>