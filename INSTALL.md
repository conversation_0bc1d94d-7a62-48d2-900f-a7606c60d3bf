# Bamboo Installation Guide

## Quick Start (MAMP Local Development)

### 1. Prerequisites
- MAMP installed and running
- PHP 8.0+ (included with MAMP)
- MySQL/MariaDB (included with MAMP)

### 2. Installation Steps

#### Step 1: Setup Project
1. Place the `bamboo` folder in your MAMP `htdocs` directory:
   ```
   /Applications/MAMP/htdocs/bamboo/  (Mac)
   C:\MAMP\htdocs\bamboo\             (Windows)
   ```

#### Step 2: Database Setup
1. Start MAMP (Apache & MySQL)
2. Open phpMyAdmin: `http://localhost/phpMyAdmin`
3. Create a new database named: `matchmaking`
4. Import the database schema:
   - Click on the `matchmaking` database
   - Go to "Import" tab
   - Choose file: `database_migration.sql`
   - Click "Go"

#### Step 3: Configuration
The application is pre-configured for MAMP with these default settings:
- **Host**: localhost
- **Database**: matchmaking
- **Username**: root
- **Password**: root
- **Port**: 3306

If your MAMP uses different settings, update `includes/config.php`.

#### Step 4: Test Installation
1. Open browser and go to: `http://localhost/bamboo/test/test_connection.php`
2. Verify all tests pass (green checkmarks)
3. If database connection fails, check your MAMP settings

#### Step 5: Access Application
- **Main Application**: `http://localhost/bamboo/`
- **User Login**: `http://localhost/bamboo/user/login/`
- **Admin Panel**: `http://localhost/bamboo/admin/` (coming soon)

### 3. Default Accounts

After running the database migration, you'll have these default accounts:

#### Admin Account
- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>

#### Test User Account
- **Username**: testuser
- **Password**: password123
- **Phone**: +**********

### 4. File Permissions

Ensure these directories are writable:
```
uploads/          (755)
uploads/products/ (755)
uploads/avatars/  (755)
uploads/logos/    (755)
logs/            (755)
```

### 5. Troubleshooting

#### Database Connection Issues
1. Check MAMP is running (green lights)
2. Verify database name is `matchmaking`
3. Check MySQL port in MAMP preferences
4. Update `includes/config.php` if needed

#### URL Routing Issues
1. Ensure `.htaccess` file exists
2. Check Apache mod_rewrite is enabled
3. Verify file permissions

#### Permission Issues
```bash
# Mac/Linux
chmod -R 755 uploads/
chmod -R 755 logs/

# Windows - Right-click folders → Properties → Security
```

## Production Deployment

### 1. Hosting Requirements
- PHP 8.0+
- MySQL 5.7+ or MariaDB 10.3+
- Apache with mod_rewrite
- SSL certificate (recommended)

### 2. Upload Files
Upload all files to your hosting account:
- Via FTP/SFTP
- Via hosting control panel file manager
- Via Git (if supported)

### 3. Database Setup
1. Create MySQL database through hosting control panel
2. Import `database_migration.sql`
3. Note database credentials

### 4. Configuration
Update `includes/config.php` with production settings:
```php
// Production database configuration
define('DB_HOST', 'your_db_host');
define('DB_NAME', 'your_db_name');
define('DB_USER', 'your_db_user');
define('DB_PASS', 'your_db_password');
```

### 5. Security
1. Change default admin password
2. Update security keys in config
3. Set `DEBUG_MODE` to `false`
4. Configure SSL/HTTPS

### 6. Testing
1. Visit: `yourdomain.com/test/test_connection.php`
2. Verify all systems are working
3. Test user registration and login

## Advanced Configuration

### Custom Domain/Subdomain
The application automatically detects the environment and adjusts URLs accordingly:
- **Subdomain**: `bamboo.yourdomain.com`
- **Subfolder**: `yourdomain.com/bamboo/`
- **Main domain**: `yourdomain.com`

### Email Configuration
Update email settings in `includes/config.php`:
```php
define('SMTP_HOST', 'your_smtp_host');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', 'your_email');
define('SMTP_PASSWORD', 'your_password');
```

### App Customization
- **App Name**: Change in admin settings or `config.php`
- **Logo**: Upload via admin panel
- **Colors**: Modify `assets/css/main.css`
- **Features**: Enable/disable in admin settings

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review error logs in `logs/error.log`
3. Test with `test/test_connection.php`
4. Ensure all requirements are met

---

**Company**: Notepadsly  
**Application**: Bamboo  
**Version**: 1.0