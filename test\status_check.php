<?php
/**
 * Quick Status Check - Fixed Issues
 */

echo "<h2>🔧 Bamboo Status Check - Post Fixes</h2>";

// Test 1: PHP Working
echo "<h3>✅ PHP Status</h3>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";

// Test 2: Config Loading
echo "<h3>📁 Config Status</h3>";
try {
    define('BAMBOO_APP', true);
    require_once 'C:\MAMP\htdocs\Bamboo\includes\config.php';
    echo "<p>✅ Config loaded successfully</p>";
    echo "<p>Base URL: " . BASE_URL . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Config error: " . $e->getMessage() . "</p>";
}

// Test 3: Database Connection
echo "<h3>🗄️ Database Status</h3>";
try {
    require_once '../includes/functions.php';
    $db = getDB();
    echo "<p>✅ Database connected successfully</p>";
    
    // Test settings table with correct columns
    $stmt = $db->query("SELECT `key`, `value` FROM settings LIMIT 3");
    $settings = $stmt->fetchAll();
    echo "<p>✅ Settings table accessible with " . count($settings) . " records</p>";
    
    foreach ($settings as $setting) {
        echo "<p>- {$setting['key']}: {$setting['value']}</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 4: App Settings Function
echo "<h3>⚙️ App Settings Function</h3>";
try {
    $app_name = getAppSetting('app_name', 'Default');
    echo "<p>✅ getAppSetting() working</p>";
    echo "<p>App Name: " . $app_name . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Settings function error: " . $e->getMessage() . "</p>";
}

// Test 5: Include Functions
echo "<h3>📄 Include Functions</h3>";
try {
    // Test if header include path works
    if (function_exists('includeHeader')) {
        echo "<p>✅ includeHeader() function exists</p>";
    } else {
        echo "<p>❌ includeHeader() function missing</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Include error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>🔗 Test Links</h3>";
echo "<p><a href='../'>Main App</a> | ";
echo "<a href='../user/login/'>Login Page</a> | ";
echo "<a href='database_check.php'>Database Check</a> | ";
echo "<a href='reset_database.php'>Reset Database</a></p>";

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>If database errors persist, use <a href='reset_database.php'>Reset Database</a></li>";
echo "<li>Test the <a href='../user/login/'>Login Page</a></li>";
echo "<li>Verify orange theme is showing</li>";
echo "</ol>";
?>