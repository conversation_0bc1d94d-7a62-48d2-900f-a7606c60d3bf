<?php
/**
 * Bamboo Web Application - Full Diagnostics Test
 * This script checks both database connectivity/schema and file permissions.
 */

// --- SETTINGS ---
define('DB_HOST', 'localhost');
define('DB_PORT', 3306);
define('DB_NAME', 'matchmaking');
define('DB_USER', 'root');
define('DB_PASS', 'root');

// Turn off web-specific warnings
error_reporting(E_ALL & ~E_WARNING & ~E_DEPRECATED);

$uploads_dir = __DIR__ . '/../uploads';
$vip_icons_dir = $uploads_dir . '/vip_icons';

// --- DATABASE CHECK ---
echo "--- DATABASE DIAGNOSTICS ---\n";
try {
    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    echo "[SUCCESS] Database connection established.\n";

    $stmt = $pdo->query("SHOW COLUMNS FROM `vip_levels` LIKE 'icon_path'");
    if ($stmt->rowCount() > 0) {
        echo "[SUCCESS] Column 'icon_path' was found in the 'vip_levels' table.\n";
    } else {
        echo "[FAILURE] Column 'icon_path' is STILL MISSING.\n";
        echo "[ACTION]  Please run this exact SQL command in phpMyAdmin:
";
        echo "          ALTER TABLE `vip_levels` ADD `icon_path` VARCHAR(255) NULL DEFAULT NULL;\n";
    }
} catch (PDOException $e) {
    echo "[FAILURE] Could not connect to the database. Error: " . $e->getMessage() . "\n";
}

// --- FILE PERMISSION CHECK ---
echo "\n--- FILE PERMISSION DIAGNOSTICS ---\n";
echo "Checking directory: $vip_icons_dir\n";

if (!file_exists($uploads_dir) || !is_dir($uploads_dir)) {
    echo "[FAILURE] The main 'uploads' directory does not exist!\n";
} else {
    echo "[INFO] Main 'uploads' directory exists.\n";
    if (!file_exists($vip_icons_dir) || !is_dir($vip_icons_dir)) {
        echo "[FAILURE] The 'vip_icons' directory inside 'uploads' does not exist!\n";
    } else {
        echo "[INFO] Directory 'vip_icons' exists.\n";
        if (is_writable($vip_icons_dir)) {
            echo "[SUCCESS] The 'vip_icons' directory is currently writable by PHP.\n";
        } else {
            echo "[FAILURE] The 'vip_icons' directory is NOT WRITABLE by PHP.\n";
            echo "[ACTION]  Attempting to set permissions automatically...\n";
            // Attempt to set permissions (works on Linux, may have limited effect on Windows)
            @chmod($vip_icons_dir, 0777);
            // Clear the stat cache and re-check
            clearstatcache();
            if (is_writable($vip_icons_dir)) {
                echo "[SUCCESS] Automatically set permissions. The directory is now writable.\n";
            } else {
                echo "[FAILURE] Could not automatically set permissions.\n";
                echo "[ACTION]  You must manually set permissions for the folder `C:\MAMP\htdocs\Bamboo\uploads\vip_icons` to allow 'Full Control' for the 'Users' group via the Security tab in the folder's Properties.\n";
            }
        }
    }
}
