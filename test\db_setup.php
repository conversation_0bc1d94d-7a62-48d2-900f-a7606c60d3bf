<?php
// Define the path to the database configuration
require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/database.php';

// Path to the SQL migration file
$sql_file = __DIR__ . '/../database_migration.sql';

// Check if the SQL file exists
if (!file_exists($sql_file)) {
    die("Error: SQL migration file not found at: " . $sql_file);
}

// Read the entire SQL file
$sql = file_get_contents($sql_file);

if ($sql === false) {
    die("Error: Unable to read the SQL migration file.");
}

// Get the database connection
    $pdo = getDB();

// Temporarily disable foreign key checks to avoid errors during table drops
$conn->query('SET foreign_key_checks = 0');

// Execute the multi-query
if ($conn->multi_query($sql)) {
    // Loop through all query results
    do {
        // Store the first result set
        if ($result = $conn->store_result()) {
            $result->free();
        }
    } while ($conn->next_result());
    echo "Database migration completed successfully!";
} else {
    echo "Error during database migration: " . $conn->error;
}

// Re-enable foreign key checks
$conn->query('SET foreign_key_checks = 1');

// Close the connection
db_close($conn);
