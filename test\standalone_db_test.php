<?php
/**
 * Bamboo Web Application - Standalone DB Connection Test
 * Version: 1.2
 */

// Basic constants for database connection
define('DB_HOST', 'localhost');
define('DB_PORT', 3306);
define('DB_NAME', 'matchmaking');
define('DB_USER', 'root');
define('DB_PASS', 'root');

echo "Attempting to connect to the database...\n";

try {
    $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    ];
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);

    echo "[SUCCESS] Database connection established successfully.\n";

    // Check for vip_levels table
    $stmt = $pdo->query("SHOW TABLES LIKE 'vip_levels'");
    if ($stmt->rowCount() > 0) {
        echo "[INFO] Table 'vip_levels' found.\n";
        
        // Check for icon_path column
        $stmt = $pdo->query("SHOW COLUMNS FROM `vip_levels` LIKE 'icon_path'");
        if ($stmt->rowCount() > 0) {
            echo "[SUCCESS] Column 'icon_path' exists in 'vip_levels' table.\n";
        } else {
            echo "[FAILURE] Column 'icon_path' is MISSING from 'vip_levels' table.\n";
            echo "[ACTION] Please run the following SQL command on your database:\n";
            echo "ALTER TABLE `vip_levels` ADD `icon_path` VARCHAR(255) NULL DEFAULT NULL AFTER `benefits`;\n";
        }
    } else {
        echo "[FAILURE] Table 'vip_levels' not found.\n";
    }

} catch (PDOException $e) {
    echo "[FAILURE] PDOException: " . $e->getMessage() . "\n";
    echo "[INFO] Please ensure your database server is running and the credentials used in this test are correct.\n";
} catch (Exception $e) {
    echo "[FAILURE] An unexpected error occurred: " . $e->getMessage() . "\n";
}
