<?php
/**
 * Bamboo Web Application - 404 Error Page
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
    require_once 'config.php';
    require_once 'functions.php';
}

$page_title = '404 - Page Not Found';
$body_class = 'error-page';

includeHeader($page_title);
?>

<div class="container-fluid h-100">
    <div class="row h-100 justify-content-center align-items-center">
        <div class="col-md-6 col-lg-4">
            <div class="text-center">
                <!-- Error Icon -->
                <div class="error-icon mb-4">
                    <i class="bi bi-exclamation-triangle-fill text-warning" style="font-size: 5rem;"></i>
                </div>
                
                <!-- Error Message -->
                <h1 class="display-4 fw-bold mb-3" style="color: #ff6900;">404</h1>
                <h2 class="h4 mb-3">Page Not Found</h2>
                <p class="text-muted mb-4">
                    Sorry, the page you are looking for doesn't exist or has been moved.
                </p>
                
                <!-- Action Buttons -->
                <div class="d-grid gap-2 d-md-block">
                    <a href="<?php echo BASE_URL; ?>" class="btn btn-primary">
                        <i class="bi bi-house-fill me-2"></i>Go Home
                    </a>
                    <button onclick="history.back()" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Go Back
                    </button>
                </div>
                
                <!-- Additional Help -->
                <div class="mt-4">
                    <small class="text-muted">
                        If you believe this is an error, please contact support.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.error-page {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ff6900 100%);
    min-height: 100vh;
}

.error-page .container-fluid {
    min-height: 100vh;
}

.error-icon {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.error-page .btn {
    min-width: 120px;
}
</style>

<?php includeFooter(); ?>