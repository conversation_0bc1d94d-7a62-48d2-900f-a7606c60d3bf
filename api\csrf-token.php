<?php
/**
 * Bamboo Web Application - CSRF Token API
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Set JSON content type
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Generate new CSRF token
    $token = generateCSRFToken();
    
    // Return success response
    echo json_encode([
        'success' => true,
        'token' => $token,
        'expires' => time() + CSRF_TOKEN_EXPIRE
    ]);
    
} catch (Exception $e) {
    // Log error
    logError('CSRF token generation failed: ' . $e->getMessage());
    
    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Failed to generate token'
    ]);
}
?>