/* ===== ENHANCED TABLE STYLING FOR ADMIN INTERFACE ===== */

/* Universal Enhanced Table Styling with Primary Color Integration */
.table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    margin-bottom: 0;
}

.table tbody tr {
    border-bottom: 2px solid #e9ecef !important;
    transition: all 0.3s ease;
    position: relative;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.05) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.02) 100%);
    transform: scale(1.002);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
    border-bottom-color: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3) !important;
}

.table tbody tr:last-child {
    border-bottom: 2px solid #e9ecef !important;
}

.table thead th {
    background: linear-gradient(135deg, var(--admin-primary, #ff6900) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.8) 100%);
    border-bottom: 3px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3) !important;
    border-top: none !important;
    font-weight: 600;
    color: white !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1.25rem 0.75rem;
}

.table td {
    vertical-align: middle;
    padding: 1.25rem 0.75rem;
    border-left: none;
    border-right: none;
    position: relative;
}

.table th {
    border-left: none;
    border-right: none;
}

/* Enhanced Serial Number Styling */
.serial-number {
    font-weight: 700;
    color: var(--admin-primary, #ff6900);
    font-size: 1rem;
    background: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.1);
    padding: 0.5rem;
    border-radius: 0.5rem;
    text-align: center;
    min-width: 40px;
    display: inline-block;
}

/* Row Separator Enhancement */
.table tbody tr::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #dee2e6 10%, #dee2e6 90%, transparent 100%);
}

.table tbody tr:last-child::after {
    display: none;
}

/* ===== ENHANCED FILTER FORM STYLING ===== */

/* Universal Filter Form Styling */
.card-header form.d-flex,
.filter-form,
.search-form {
    background: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.02) !important;
    padding: 1rem !important;
    border-radius: 1rem !important;
    border: 2px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.1) !important;
    box-shadow: 0 0.125rem 0.5rem rgba(var(--admin-primary-rgb, 255, 105, 0), 0.08) !important;
    gap: 0.75rem !important;
    margin-top: 1rem !important;
}

.card-header form.d-flex .form-control,
.card-header form.d-flex .form-select,
.filter-form .form-control,
.filter-form .form-select,
.search-form .form-control,
.search-form .form-select {
    border: 2px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.15) !important;
    border-radius: 0.75rem !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.875rem !important;
    transition: all 0.3s ease !important;
    background: white !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.05) !important;
    min-width: 150px;
}

.card-header form.d-flex .form-control:focus,
.card-header form.d-flex .form-select:focus,
.filter-form .form-control:focus,
.filter-form .form-select:focus,
.search-form .form-control:focus,
.search-form .form-select:focus {
    border-color: var(--admin-primary, #ff6900) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--admin-primary-rgb, 255, 105, 0), 0.25) !important;
    background: white !important;
    transform: translateY(-1px) !important;
}

.card-header form.d-flex .btn,
.filter-form .btn,
.search-form .btn {
    border-radius: 0.75rem !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.1) !important;
}

.card-header form.d-flex .btn:hover,
.filter-form .btn:hover,
.search-form .btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3) !important;
}

/* ===== PAGINATION STYLING ===== */

.pagination {
    margin-top: 1.5rem;
    justify-content: center;
}

.pagination .page-link {
    border: 2px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.15);
    border-radius: 0.5rem;
    margin: 0 0.25rem;
    padding: 0.75rem 1rem;
    color: var(--admin-primary, #ff6900);
    background: white;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.1);
    border-color: var(--admin-primary, #ff6900);
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: var(--admin-primary, #ff6900);
    border-color: var(--admin-primary, #ff6900);
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background: #f8f9fa;
    border-color: #dee2e6;
}

/* ===== RESPONSIVE TABLE ENHANCEMENTS ===== */

.table-responsive {
    border-radius: 0.75rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

@media (max-width: 768px) {
    .table td,
    .table th {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .serial-number {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
        min-width: 30px;
    }
    
    .card-header form.d-flex,
    .filter-form,
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .card-header form.d-flex .form-control,
    .card-header form.d-flex .form-select,
    .filter-form .form-control,
    .filter-form .form-select,
    .search-form .form-control,
    .search-form .form-select {
        min-width: auto;
        margin-bottom: 0.5rem;
    }
}

/* ===== STATUS BADGES ENHANCEMENT ===== */

.badge {
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545, #e83e8c) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8, #6f42c1) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--admin-primary, #ff6900), var(--admin-accent, #007bff)) !important;
}
