-- Insert sample tasks with correct user IDs
INSERT INTO tasks (user_id, product_id, amount, commission_earned, base_commission, vip_bonus, status, assigned_at, started_at, completed_at, expires_at, submission_data, admin_notes) VALUES
(4, 1, 50.00, 5.00, 5.00, 0.00, 'completed', NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), NULL, 'Task completed by <PERSON>'),
(5, 2, 80.00, 8.00, 8.00, 0.00, 'completed', NOW(), NOW(), NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), NULL, 'Task completed by <PERSON>');

-- Insert sample transactions with correct user IDs
INSERT INTO transactions (user_id, type, amount, balance_before, balance_after, status, payment_method, transaction_id, created_at, updated_at) VALUES
(4, 'deposit', 200.00, 0.00, 200.00, 'completed', 'bank', 'TXN000001', NOW(), NOW()),
(5, 'deposit', 300.00, 0.00, 300.00, 'completed', 'bank', 'TXN000002', NOW(), NOW()),
(4, 'withdrawal', 50.00, 200.00, 150.00, 'completed', 'bank', 'TXN000003', NOW(), NOW()),
(5, 'withdrawal', 100.00, 300.00, 200.00, 'completed', 'bank', 'TXN000004', NOW(), NOW());
