/**
 * Bamboo Web Application - Payment Card Management Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== PAYMENT CARD MANAGEMENT STYLES ===== */

/* Page Header */
.page-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.04);
}

.page-header h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--admin-text-dark), rgba(44, 62, 80, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Payment Card Management Card */
.payment-card-management {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    overflow: hidden;
}

.payment-card-management:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
}

.payment-card-management .card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1.25rem 1.5rem;
    position: relative;
}

.payment-card-management .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.payment-card-management .card-body {
    padding: 0;
}

/* Modern Table Styling */
.payment-card-table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    font-size: 0.875rem;
}

.payment-card-table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: var(--admin-text-dark);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-size: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.payment-card-table thead th:first-child {
    border-top-left-radius: 0.75rem;
}

.payment-card-table thead th:last-child {
    border-top-right-radius: 0.75rem;
}

.payment-card-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.payment-card-table tbody tr:hover {
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    transform: scale(1.01);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08);
}

.payment-card-table tbody tr:last-child {
    border-bottom: none;
}

.payment-card-table tbody td {
    padding: 1rem 0.75rem;
    border: none;
    vertical-align: middle;
    line-height: 1.4;
}

/* Member Info Styling */
.member-info {
    font-size: 0.875rem;
    line-height: 1.3;
}

.member-info .username {
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 0.25rem;
    text-transform: capitalize;
}

.member-info .contact {
    color: var(--admin-text-muted);
    font-size: 0.75rem;
}

/* Exchange Name */
.exchange-name {
    font-weight: 500;
    color: var(--admin-text-dark);
    background: rgba(0, 0, 0, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.8rem;
    display: inline-block;
}

/* Wallet Address */
.wallet-address {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.5rem;
    border-radius: 0.375rem;
    word-break: break-all;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.3s ease;
}

.wallet-address:hover {
    background: rgba(var(--admin-primary-rgb), 0.1);
    white-space: normal;
    overflow: visible;
    max-width: none;
}

/* Binding Time */
.binding-time {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    white-space: nowrap;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons .btn {
    border-radius: 0.5rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-buttons .btn:hover::before {
    left: 100%;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
}

.action-buttons .btn-outline-primary {
    border-color: rgba(79, 70, 229, 0.2);
    color: #4f46e5;
    background: rgba(79, 70, 229, 0.05);
}

.action-buttons .btn-outline-primary:hover {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
    border-color: #4f46e5;
    color: white;
    box-shadow: 0 0.5rem 1.5rem rgba(79, 70, 229, 0.3);
}

.action-buttons .btn-outline-secondary {
    border-color: rgba(108, 117, 125, 0.2);
    color: #6c757d;
    background: rgba(108, 117, 125, 0.05);
}

.action-buttons .btn-outline-secondary:hover {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border-color: #6c757d;
    color: white;
    box-shadow: 0 0.5rem 1.5rem rgba(108, 117, 125, 0.3);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--admin-text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .payment-card-table {
        font-size: 0.8rem;
    }
    
    .payment-card-table thead th,
    .payment-card-table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .wallet-address {
        max-width: 150px;
    }
}

@media (max-width: 768px) {
    .payment-card-table {
        font-size: 0.75rem;
    }
    
    .payment-card-table thead th,
    .payment-card-table tbody td {
        padding: 0.5rem 0.25rem;
    }
    
    .member-info,
    .exchange-name,
    .wallet-address {
        font-size: 0.7rem;
    }
    
    .wallet-address {
        max-width: 100px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .action-buttons .btn {
        width: 100%;
        font-size: 0.7rem;
    }
}

/* Loading States */
.table-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Card hover effects */
.payment-card-management {
    position: relative;
    overflow: hidden;
}

.payment-card-management::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--admin-primary), transparent);
    transition: left 0.5s ease;
}

.payment-card-management:hover::before {
    left: 100%;
}
