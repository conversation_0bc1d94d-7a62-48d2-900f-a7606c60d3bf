<?php
/**
 * Bamboo Web Application - Common Header
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get app settings
$app_name = getAppSetting('app_name', APP_NAME);
$app_logo = getAppSetting('app_logo', '');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title><?php echo htmlspecialchars($page_title ?? $app_name); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>images/favicon.ico">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="<?php echo ASSETS_URL; ?>css/main.css" rel="stylesheet">
    
    <!-- Additional CSS files -->
    <?php if (isset($additional_css) && is_array($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link href="<?php echo $css_file; ?>" rel="stylesheet">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Meta tags for mobile app feel -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="<?php echo htmlspecialchars($app_name); ?>">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#ff6900">
    
    <!-- PWA Manifest -->
    <link rel="manifest" href="<?php echo BASE_URL; ?>manifest.json">
    
    <!-- Preload critical resources -->
    <link rel="preload" href="<?php echo ASSETS_URL; ?>css/main.css" as="style">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
</head>
<body class="<?php echo $body_class ?? ''; ?>">
    
    <!-- Loading Spinner -->
    <div id="loading-spinner" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    
    <!-- Flash Messages -->
    <?php $flash_messages = getFlashMessages(); ?>
    <?php if (!empty($flash_messages)): ?>
        <div class="flash-messages">
            <?php if (isset($flash_messages['success'])): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['success']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
            
            <?php if (isset($flash_messages['error'])): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <?php echo htmlspecialchars($flash_messages['error']); ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
    
    <!-- CSRF Token for AJAX requests -->
    <meta name="csrf-token" content="<?php echo generateCSRFToken(); ?>">
    
    <!-- Global JavaScript Variables - Initialize before any JS loads -->
    <script>
        // Global configuration for JavaScript
        window.BambooApp = window.BambooApp || {};
        
        // Set configuration properties
        BambooApp.baseUrl = '<?php echo BASE_URL; ?>';
        BambooApp.assetsUrl = '<?php echo ASSETS_URL; ?>';
        BambooApp.csrfToken = '<?php echo generateCSRFToken(); ?>';
        BambooApp.isLoggedIn = <?php echo isLoggedIn() ? 'true' : 'false'; ?>;
        BambooApp.isAdmin = <?php echo isAdminLoggedIn() ? 'true' : 'false'; ?>;
        BambooApp.userId = <?php echo getCurrentUserId() ?? 'null'; ?>;
        BambooApp.appName = '<?php echo htmlspecialchars(getAppSetting('app_name', APP_NAME)); ?>';
    </script>
    
    <!-- Main Content Container -->
    <div id="main-container" class="main-container">