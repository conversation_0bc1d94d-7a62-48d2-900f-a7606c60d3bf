<?php
/**
 * Bamboo Web Application - Adjust Balance API
 * Company: Notepadsly
 * Version: 1.0
 */

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$user_id = (int)($_POST['user_id'] ?? 0);
$amount = (float)($_POST['amount'] ?? 0);
$type = $_POST['type'] ?? '';

if ($user_id <= 0 || $amount <= 0 || empty($type)) {
    jsonResponse(['success' => false, 'message' => 'Invalid parameters.'], 400);
}

try {
    beginTransaction();

    // Adjust user balance
    $admin_id = $_SESSION['admin_id'] ?? null; // Get the logged-in admin's ID
    $adjustment_success = adjustUserBalance($user_id, $amount, $type, $admin_id);

    if (!$adjustment_success) {
        rollbackTransaction();
        $error = getLastError();
        if (isset($error['message'])) {
            jsonResponse(['success' => false, 'message' => $error['message']], 500);
        } else {
            jsonResponse(['success' => false, 'message' => 'Failed to adjust balance.'], 500);
        }
    }

    commitTransaction();
    jsonResponse(['success' => true, 'message' => 'Balance adjusted successfully!']);

} catch (Exception $e) {
    rollbackTransaction();
    logError('Error adjusting balance: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}
?>
