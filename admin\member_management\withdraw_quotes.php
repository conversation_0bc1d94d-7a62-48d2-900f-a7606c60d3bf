<?php
/**
 * Bamboo Web Application - Withdrawal Quotes Management
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

$user_id = (int)($_GET['id'] ?? 0);
if ($user_id === 0) {
    redirect('admin/member_management/');
}

$user = fetchRow('SELECT username, balance, avatar, tasks_completed_today FROM users WHERE id = ?', [$user_id]);
if (!$user) {
    redirect('admin/member_management/');
}

// Get user's task progress
$task_progress = fetchRow("SELECT COUNT(*) as completed_tasks FROM task_completions WHERE user_id = ? AND DATE(completed_at) = CURDATE()", [$user_id]);
$completed_tasks = $task_progress ? $task_progress['completed_tasks'] : 0;

// Get user's VIP level to determine max daily tasks
$vip_info = fetchRow("SELECT v.max_daily_tasks FROM users u LEFT JOIN vip_levels v ON u.vip_level = v.level WHERE u.id = ?", [$user_id]);
$max_daily_tasks = $vip_info ? $vip_info['max_daily_tasks'] : 0;

// Get appearance settings for gradient colors
$gradient_start = getAppSetting('appearance_gradient_start', '#ff6900');
$gradient_end = getAppSetting('appearance_gradient_end', '#ff8533');

$page_title = 'Withdrawal Quotes for ' . htmlspecialchars($user['username']);
$additional_css = [BASE_URL . 'admin/assets/css/member-details.css'];
include '../includes/admin_header.php';

// Handle form submission for adding withdrawal quote
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $message = sanitizeInput($_POST['message']);
        if (!empty($message)) {
            insertRecord('withdrawal_quotes', [
                'user_id' => $user_id,
                'message' => $message,
                'admin_id_created' => $_SESSION['admin_id']
            ]);
            showSuccess('Withdrawal quote added successfully!');
        } else {
            showError('Message cannot be empty.');
        }
        redirect('admin/member_management/withdraw_quotes.php?id=' . $user_id);
        exit();
    }
}

// Handle resolution or deletion of withdrawal quote
if (isset($_GET['action']) && isset($_GET['quote_id'])) {
    $quote_id = (int)($_GET['quote_id'] ?? 0);
    if ($quote_id > 0) {
        if ($_GET['action'] === 'resolve') {
            updateRecord('withdrawal_quotes', ['status' => 'resolved'], 'id = ? AND user_id = ?', [$quote_id, $user_id]);
            showSuccess('Withdrawal quote resolved successfully!');
        } elseif ($_GET['action'] === 'delete') {
            deleteRecord('withdrawal_quotes', 'id = ? AND user_id = ?', [$quote_id, $user_id]);
            showSuccess('Withdrawal quote deleted successfully!');
        }
    } else {
        showError('Invalid quote ID.');
    }
    redirect('admin/member_management/withdraw_quotes.php?id=' . $user_id);
    exit();
}

$quotes = fetchAll('SELECT * FROM withdrawal_quotes WHERE user_id = ? ORDER BY created_at DESC', [$user_id]);

?>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="d-flex align-items-center">
                        <div class="user-avatar-header me-3">
                            <?php if (!empty($user['avatar'])): ?>
                                <img src="<?php echo BASE_URL . 'uploads/avatars/' . $user['avatar']; ?>" alt="Avatar" class="header-avatar">
                            <?php else: ?>
                                <div class="header-avatar-initials">
                                    <?php echo strtoupper(substr($user['username'], 0, 2)); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h1 class="h3 mb-0">
                                <i class="bi bi-cash-coin text-info me-2"></i>
                                Withdrawal Quotes: <?php echo htmlspecialchars($user['username']); ?>
                            </h1>
                            <small class="text-muted">Manage withdrawal quotes and messages</small>
                        </div>
                    </div>
                    <a href="view.php?id=<?php echo $user_id; ?>" class="btn btn-secondary"><i class="bi bi-arrow-left me-2"></i>Back to User Details</a>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-circle me-2"></i>User Information
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Username</div>
                                    <div class="info-value"><?php echo htmlspecialchars($user['username']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Wallet Balance</div>
                                    <div class="info-value text-success fw-bold"><?php echo formatCurrency($user['balance']); ?></div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">Daily Task Progress</div>
                                    <div class="info-value">
                                        <span class="task-progress">
                                            <span class="completed-tasks text-primary fw-bold"><?php echo $completed_tasks; ?></span>
                                            <span class="task-separator">/</span>
                                            <span class="total-tasks text-muted"><?php echo $max_daily_tasks; ?></span>
                                        </span>
                                        <div class="progress mt-2" style="height: 6px;">
                                            <div class="progress-bar bg-primary" role="progressbar"
                                                 style="width: <?php echo $max_daily_tasks > 0 ? ($completed_tasks / $max_daily_tasks * 100) : 0; ?>%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header text-white" style="background: linear-gradient(135deg, <?php echo $gradient_start; ?>, <?php echo $gradient_end; ?>);">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-chat-quote me-2"></i>Add New Withdrawal Quote
                        </h5>
                        <small class="opacity-75">Create withdrawal quote or message for user</small>
                    </div>
                    <div class="card-body">
                        <form action="withdraw_quotes.php?id=<?php echo $user_id; ?>" method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <div class="mb-3">
                                <label for="message" class="form-label">
                                    <i class="bi bi-chat-text text-primary me-1"></i>Quote Message
                                </label>
                                <textarea class="form-control" id="message" name="message" rows="4"
                                          placeholder="Enter withdrawal quote or message for the user..." required></textarea>
                                <div class="form-text">This message will be associated with the user's withdrawal requests</div>
                            </div>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-info">
                                    <i class="bi bi-chat-quote me-2"></i>Add Quote
                                </button>
                                <button type="reset" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Reset Form
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Existing Withdrawal Quotes</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($quotes)): ?>
                            <p class="text-muted">No withdrawal quotes found for this user.</p>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>S/N</th>
                                            <th>Message</th>
                                            <th>Status</th>
                                            <th>Creation Date</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $sn = 1; foreach ($quotes as $quote): ?>
                                            <tr>
                                                <td><?php echo $sn++; ?></td>
                                                <td><?php echo htmlspecialchars($quote['message']); ?></td>
                                                <td><?php echo $quote['status'] === 'active' ? '<span class="badge bg-warning">Active</span>' : '<span class="badge bg-success">Resolved</span>'; ?></td>
                                                <td><?php echo formatDate($quote['created_at']); ?></td>
                                                <td>
                                                    <?php if ($quote['status'] === 'active'): ?>
                                                        <a href="withdraw_quotes.php?id=<?php echo $user_id; ?>&action=resolve&quote_id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-success">Resolve</a>
                                                    <?php endif; ?>
                                                    <a href="withdraw_quotes.php?id=<?php echo $user_id; ?>&action=delete&quote_id=<?php echo $quote['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this quote?')">Delete</a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php include '../includes/admin_footer_scripts.php'; ?>
