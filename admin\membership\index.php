<?php
/**
 * Bamboo Web Application - Membership Level Management
 * Company: Notepadsly
 * Version: 1.3 - Reworked file upload logic
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('admin/login/');
}

// --- Reworked Form Submission Logic ---
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['csrf_token'])) {
    if (!verifyCSRFToken($_POST['csrf_token'])) {
        showError('Invalid security token. Please try again.');
    } else {
        $action = $_POST['action'] ?? '';
        $level_id = (int)($_POST['level_id'] ?? 0);

        // 1. Handle File Upload First (like the test script)
        $icon_path = $_POST['existing_icon_path'] ?? null;
        $upload_error = null;

        if (isset($_FILES['level_icon']) && $_FILES['level_icon']['error'] === UPLOAD_ERR_OK) {
            // Use the absolute path for the upload directory
            $upload_dir = 'vip_icons/';
            $result = handleFileUpload($_FILES['level_icon'], $upload_dir);
            if ($result['success']) {
                $icon_path = $result['filename'];
            } else {
                // Don't stop execution, just record the error
                $upload_error = $result['message'];
                showError("Icon upload failed: " . $upload_error);
            }
        }

        // 2. Process form data only if there was no upload error
        if ($upload_error === null) {
            if ($action === 'add_level' || $action === 'edit_level') {
                $level_name = sanitizeInput($_POST['level_name'] ?? '');
                $level_percentage = (float)($_POST['level_percentage'] ?? 0);
                $level_min_amount = (float)($_POST['level_min_amount'] ?? 0);
                $level_tasks = (int)($_POST['level_tasks'] ?? 0);
                $vip_introduction = sanitizeInput($_POST['vip_introduction'] ?? '');

                $level_data = [
                    'name' => $level_name,
                    'min_balance' => $level_min_amount,
                    'max_daily_tasks' => $level_tasks,
                    'commission_multiplier' => ($level_percentage / 100),
                    'benefits' => $vip_introduction,
                    'icon_path' => $icon_path
                ];

                try {
                    if ($action === 'add_level') {
                        $max_level_row = fetchRow("SELECT MAX(level) as max_level FROM vip_levels");
                        $level_data['level'] = ($max_level_row['max_level'] ?? 0) + 1;
                        if (insertRecord('vip_levels', $level_data)) {
                            showSuccess('VIP level added successfully!');
                        }
                    } elseif ($action === 'edit_level' && $level_id > 0) {
                        if (updateRecord('vip_levels', $level_data, 'id = ?', [$level_id])) {
                            showSuccess('VIP level updated successfully!');
                        }
                    }
                } catch (Exception $e) {
                    showError('Database error: ' . $e->getMessage());
                }
            } elseif ($action === 'delete_level' && $level_id > 0) {
                // You might want to delete the icon file from the server here
                deleteRecord('vip_levels', 'id = ?', [$level_id]);
                showSuccess('VIP level deleted successfully!');
            }
        }
    }
    redirect('admin/membership/');
    exit();
}

$vip_levels = fetchAll("SELECT * FROM vip_levels ORDER BY level ASC");
$page_title = 'Membership Level Management';
include '../includes/admin_header.php';
?>

<style>
/* View Toggle Styling */
.btn-group .btn.active {
    background: var(--admin-primary, #ff6900) !important;
    border-color: var(--admin-primary, #ff6900) !important;
    color: white !important;
}

.view-container {
    transition: all 0.3s ease;
}

/* Enhanced List View Table Styling */
#listView .table {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

#listView .table th {
    background: linear-gradient(135deg, var(--admin-primary, #ff6900) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.8) 100%);
    border-bottom: 3px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3);
    border-top: none;
    font-weight: 600;
    color: white !important;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 1.25rem 0.75rem;
}

#listView .table td {
    vertical-align: middle;
    border-bottom: 2px solid #e9ecef;
    padding: 1.25rem 0.75rem;
    position: relative;
}

#listView .table tbody tr {
    transition: all 0.3s ease;
    position: relative;
}

#listView .table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
}

#listView .table-hover tbody tr:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.05) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.02) 100%);
    transform: scale(1.002);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
}

/* Row Separator Enhancement */
#listView .table tbody tr::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #dee2e6 20%, #dee2e6 80%, transparent 100%);
}

#listView .table tbody tr:last-child::after {
    display: none;
}

/* Enhanced Badge Styling */
.badge.fs-6 {
    font-size: 0.875rem !important;
    padding: 0.5rem 0.75rem;
}

/* Action Button Styling */
.btn-group .btn {
    border-radius: 0.375rem;
}

.btn-group .btn:first-child {
    margin-right: 0.25rem;
}

/* Enhanced Button Colors */
.btn-outline-primary {
    border-color: var(--admin-primary, #ff6900) !important;
    color: var(--admin-primary, #ff6900) !important;
}

.btn-outline-primary:hover {
    background: var(--admin-primary, #ff6900) !important;
    border-color: var(--admin-primary, #ff6900) !important;
    color: white !important;
}

.btn-outline-secondary {
    border-color: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3) !important;
    color: var(--admin-primary, #ff6900) !important;
}

.btn-outline-secondary:hover {
    background: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.1) !important;
    border-color: var(--admin-primary, #ff6900) !important;
    color: var(--admin-primary, #ff6900) !important;
}
</style>

<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container-fluid">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 mb-0">Membership Level Management</h1>
                    <div class="d-flex align-items-center gap-3">
                        <!-- View Toggle Buttons -->
                        <div class="btn-group" role="group" aria-label="View toggle">
                            <button type="button" class="btn btn-outline-secondary" id="gridViewBtn" onclick="switchView('grid')">
                                <i class="bi bi-grid-3x3-gap me-1"></i>Grid
                            </button>
                            <button type="button" class="btn btn-outline-secondary" id="listViewBtn" onclick="switchView('list')">
                                <i class="bi bi-list-ul me-1"></i>List
                            </button>
                        </div>
                        <button type="button" class="btn btn-primary" onclick="setupAddModal()">
                            <i class="bi bi-plus-circle me-2"></i>Add Level
                        </button>
                    </div>
                </div>
                <!-- Grid View -->
                <div id="gridView" class="view-container">
                    <div class="row">
                        <?php if (!empty($vip_levels)):
                            $colors = ['#fde2e4', '#fad2e1', '#e2ece9', '#bee1e6', '#cddafd', '#dfe7fd'];
                            foreach ($vip_levels as $i => $level):
                                $bg_color = $colors[$i % count($colors)]; ?>
                                <div class="col-lg-4 col-md-6 mb-4">
                                    <div class="card h-100" style="background-color: <?php echo $bg_color; ?>;">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h5 class="card-title mb-0">
                                                <?php if (!empty($level['icon_path'])): ?>
                                                    <img src="<?php echo BASE_URL . 'uploads/vip_icons/' . htmlspecialchars($level['icon_path']); ?>" alt="<?php echo htmlspecialchars($level['name']); ?>" style="width: 24px; height: 24px; border-radius: 50%; margin-right: 8px;">
                                                <?php endif; ?>
                                                <?php echo htmlspecialchars($level['name']); ?>
                                            </h5>
                                            <span class="badge bg-primary">Level <?php echo $level['level']; ?></span>
                                        </div>
                                        <div class="card-body">
                                            <p><strong>Min. Balance:</strong> <span class="text-info"><?php echo formatCurrency($level['min_balance'] ?? 0); ?></span></p>
                                            <p><strong>Daily Tasks:</strong> <span class="text-warning"><?php echo number_format($level['max_daily_tasks'] ?? 0); ?></span></p>
                                            <p><strong>Commission Rate:</strong> <span class="text-success"><?php echo number_format(($level['commission_multiplier'] ?? 0) * 100, 2); ?>%</span></p>
                                            <?php if (!empty($level['benefits'])): ?>
                                                <p><strong>Introduction:</strong> <small class="text-muted"><?php echo htmlspecialchars($level['benefits']); ?></small></p>
                                            <?php endif; ?>
                                            <div class="d-flex justify-content-between mt-3">
                                                <button type="button" class="btn btn-outline-primary btn-sm" onclick='editLevel(<?php echo json_encode($level); ?>)'><i class="bi bi-pencil"></i> Edit</button>
                                                <button type="button" class="btn btn-outline-danger btn-sm" onclick="deleteLevel(<?php echo $level['id']; ?>, '<?php echo htmlspecialchars(addslashes($level['name'])); ?>')"><i class="bi bi-trash"></i> Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <div class="col-12">
                                <div class="text-center py-5">
                                    <i class="bi bi-award display-1 text-muted"></i>
                                    <h4 class="mt-3">No VIP Levels Found</h4>
                                    <p class="text-muted">Create your first VIP level to get started.</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- List View -->
                <div id="listView" class="view-container" style="display: none;">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-award me-2"></i>VIP Levels (<?php echo count($vip_levels); ?>)
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (!empty($vip_levels)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Level</th>
                                                <th>Name</th>
                                                <th>Icon</th>
                                                <th>Min. Balance</th>
                                                <th>Daily Tasks</th>
                                                <th>Commission Rate</th>
                                                <th>Introduction</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($vip_levels as $level): ?>
                                                <tr>
                                                    <td>
                                                        <span class="badge bg-primary fs-6">Level <?php echo $level['level']; ?></span>
                                                    </td>
                                                    <td>
                                                        <strong><?php echo htmlspecialchars($level['name']); ?></strong>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($level['icon_path'])): ?>
                                                            <img src="<?php echo BASE_URL . 'uploads/vip_icons/' . htmlspecialchars($level['icon_path']); ?>"
                                                                 alt="<?php echo htmlspecialchars($level['name']); ?>"
                                                                 style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;">
                                                        <?php else: ?>
                                                            <div class="bg-secondary rounded-circle d-inline-flex align-items-center justify-content-center"
                                                                 style="width: 32px; height: 32px;">
                                                                <i class="bi bi-award text-white"></i>
                                                            </div>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="text-info fw-bold"><?php echo formatCurrency($level['min_balance'] ?? 0); ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="text-warning fw-bold"><?php echo number_format($level['max_daily_tasks'] ?? 0); ?></span>
                                                    </td>
                                                    <td>
                                                        <span class="text-success fw-bold"><?php echo number_format(($level['commission_multiplier'] ?? 0) * 100, 2); ?>%</span>
                                                    </td>
                                                    <td>
                                                        <?php if (!empty($level['benefits'])): ?>
                                                            <span class="text-muted" title="<?php echo htmlspecialchars($level['benefits']); ?>">
                                                                <?php echo strlen($level['benefits']) > 50 ? substr(htmlspecialchars($level['benefits']), 0, 50) . '...' : htmlspecialchars($level['benefits']); ?>
                                                            </span>
                                                        <?php else: ?>
                                                            <span class="text-muted">-</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <button type="button" class="btn btn-outline-primary btn-sm"
                                                                    onclick='editLevel(<?php echo json_encode($level); ?>)'
                                                                    title="Edit Level">
                                                                <i class="bi bi-pencil"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-outline-danger btn-sm"
                                                                    onclick="deleteLevel(<?php echo $level['id']; ?>, '<?php echo htmlspecialchars(addslashes($level['name'])); ?>')"
                                                                    title="Delete Level">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="bi bi-award display-1 text-muted"></i>
                                    <h4 class="mt-3">No VIP Levels Found</h4>
                                    <p class="text-muted">Create your first VIP level to get started.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<!-- Add/Edit Level Modal -->
<div class="modal fade" id="levelModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="levelForm" method="POST" enctype="multipart/form-data" action="index.php">
                <div class="modal-header">
                    <h5 class="modal-title" id="levelModalTitle">Add New VIP Level</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="action" id="formAction" value="add_level">
                    <input type="hidden" name="level_id" id="levelId" value="0">
                    <input type="hidden" name="existing_icon_path" id="existingIconPath" value="">
                    
                    <div class="mb-3"><label for="levelName" class="form-label">Level Name</label><input type="text" class="form-control" id="levelName" name="level_name" required></div>
                    <div class="mb-3"><label for="levelPercentage" class="form-label">Level Percentage</label><div class="input-group"><input type="number" class="form-control" id="levelPercentage" name="level_percentage" step="0.01" min="0" required><span class="input-group-text">%</span></div></div>
                    <div class="mb-3"><label for="levelMinAmount" class="form-label">Level Minimum Amount</label><div class="input-group"><span class="input-group-text">USDT</span><input type="number" class="form-control" id="levelMinAmount" name="level_min_amount" step="0.01" min="0" required></div></div>
                    <div class="mb-3"><label for="levelTasks" class="form-label">Level Tasks</label><input type="number" class="form-control" id="levelTasks" name="level_tasks" min="1" required></div>
                    <div class="mb-3"><label for="levelIcon" class="form-label">Level Icon</label><input type="file" class="form-control" id="levelIcon" name="level_icon" accept="image/*"></div>
                    <div class="mb-3"><label for="vipIntroduction" class="form-label">Vip Introduction (Quote)</label><textarea class="form-control" id="vipIntroduction" name="vip_introduction" rows="3"></textarea></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="submit" class="btn btn-primary">Submit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<form id="deleteForm" method="POST" action="index.php"><input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>"><input type="hidden" name="action" value="delete_level"><input type="hidden" name="level_id" id="deleteLevelId"></form>

<script>
// View switching function - Define before DOMContentLoaded
window.switchView = function(viewType) {
    const gridView = document.getElementById('gridView');
    const listView = document.getElementById('listView');
    const gridBtn = document.getElementById('gridViewBtn');
    const listBtn = document.getElementById('listViewBtn');

    if (viewType === 'list') {
        gridView.style.display = 'none';
        listView.style.display = 'block';
        gridBtn.classList.remove('active');
        listBtn.classList.add('active');
        localStorage.setItem('membershipView', 'list');
    } else {
        gridView.style.display = 'block';
        listView.style.display = 'none';
        gridBtn.classList.add('active');
        listBtn.classList.remove('active');
        localStorage.setItem('membershipView', 'grid');
    }
}

document.addEventListener('DOMContentLoaded', function () {
    const levelModal = new bootstrap.Modal(document.getElementById('levelModal'));
    const levelForm = document.getElementById('levelForm');

    // Initialize view based on localStorage or default to grid
    const savedView = localStorage.getItem('membershipView') || 'grid';
    switchView(savedView);

    window.setupAddModal = function() {
        levelForm.reset();
        document.getElementById('levelModalTitle').textContent = 'Add New VIP Level';
        document.getElementById('formAction').value = 'add_level';
        document.getElementById('levelId').value = '0';
        document.getElementById('existingIconPath').value = '';
        levelModal.show();
    }

    window.editLevel = function(level) {
        levelForm.reset();
        document.getElementById('levelModalTitle').textContent = 'Edit VIP Level';
        document.getElementById('formAction').value = 'edit_level';
        document.getElementById('levelId').value = level.id;
        
        document.getElementById('levelName').value = level.name;
        document.getElementById('levelPercentage').value = (level.commission_multiplier * 100).toFixed(2);
        document.getElementById('levelMinAmount').value = level.min_balance;
        document.getElementById('levelTasks').value = level.max_daily_tasks;
        document.getElementById('vipIntroduction').value = level.benefits;
        document.getElementById('existingIconPath').value = level.icon_path || '';
        
        levelModal.show();
    }

    window.deleteLevel = function(id, name) {
        if (confirm(`Are you sure you want to delete "${name}"? This cannot be undone.`)) {
            document.getElementById('deleteLevelId').value = id;
            document.getElementById('deleteForm').submit();
        }
    }
});
</script>

<?php include '../includes/admin_footer_scripts.php'; ?>
