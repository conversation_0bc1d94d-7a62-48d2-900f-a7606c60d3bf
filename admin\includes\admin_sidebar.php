<?php
/**
 * Bamboo Web Application - Admin Sidebar
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

// Get current page for active menu highlighting
$current_page = basename($_SERVER['REQUEST_URI']);
$current_section = explode('/', trim($_SERVER['REQUEST_URI'], '/'))[1] ?? '';

// Get app settings
$app_name = getAppSetting('app_name', APP_NAME);
$app_logo = getAppSetting('app_logo', '');
?>

<div class="admin-sidebar">
    <!-- Logo/Brand -->
    <div class="sidebar-brand">
        <a href="<?php echo BASE_URL; ?>admin/dashboard/" class="brand-link">
            <?php if ($app_logo): ?>
                <?php
                // Check if the logo path already includes the full URL
                if (strpos($app_logo, 'http') === 0) {
                    $logo_path = $app_logo;
                } else {
                    $logo_path = BASE_URL . 'uploads/logos/' . $app_logo;
                }
                $file_extension = strtolower(pathinfo($app_logo, PATHINFO_EXTENSION));
                ?>
                <div class="brand-content">
                    <?php if ($file_extension === 'svg'): ?>
                        <div class="brand-logo-svg">
                            <object data="<?php echo $logo_path; ?>" type="image/svg+xml" class="brand-logo">
                                <img src="<?php echo $logo_path; ?>" alt="<?php echo $app_name; ?>" class="brand-logo">
                            </object>
                        </div>
                    <?php else: ?>
                        <img src="<?php echo $logo_path; ?>" alt="<?php echo $app_name; ?>" class="brand-logo">
                    <?php endif; ?>
                    <small class="brand-subtitle">Admin Panel</small>
                </div>
            <?php else: ?>
                <div class="brand-content">
                    <div class="brand-text-container">
                        <span class="brand-text"><?php echo htmlspecialchars($app_name); ?></span>
                        <small class="brand-subtitle">Admin Panel</small>
                    </div>
                </div>
            <?php endif; ?>
        </a>
    </div>
    
    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav flex-column">
            
            <!-- Dashboard -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/dashboard/" 
                   class="nav-link <?php echo $current_section === 'dashboard' ? 'active' : ''; ?>">
                    <i class="bi bi-house"></i>
                    <span>Home</span>
                </a>
            </li>
            
            <!-- Basic Information -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/settings/"
                   class="nav-link <?php echo $current_section === 'settings' ? 'active' : ''; ?>">
                    <i class="bi bi-sliders"></i>
                    <span>Basic Information</span>
                </a>
            </li>

            <!-- Appearance Settings -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/appearance/"
                   class="nav-link <?php echo $current_section === 'appearance' ? 'active' : ''; ?>">
                    <i class="bi bi-palette"></i>
                    <span>Appearance</span>
                </a>
            </li>
            
            <!-- Distribution Settings -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/distribution/" 
                   class="nav-link <?php echo $current_section === 'distribution' ? 'active' : ''; ?>">
                    <i class="bi bi-diagram-3"></i>
                    <span>Distribution Settings</span>
                </a>
            </li>
            
            <!-- Membership Level -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/membership/" 
                   class="nav-link <?php echo $current_section === 'membership' ? 'active' : ''; ?>">
                    <i class="bi bi-star"></i>
                    <span>Membership Level</span>
                </a>
            </li>
            
            <!-- Platform Customer -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/customer_service/" 
                   class="nav-link <?php echo $current_section === 'customer_service' ? 'active' : ''; ?>">
                    <i class="bi bi-headset"></i>
                    <span>Platform Customer</span>
                </a>
            </li>
            
            <!-- Withdraw Policy -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/withdrawal_policy/" 
                   class="nav-link <?php echo $current_section === 'withdrawal_policy' ? 'active' : ''; ?>">
                    <i class="bi bi-shield-check"></i>
                    <span>Withdraw Policy</span>
                </a>
            </li>
            
            <!-- Member Management -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/member_management/" 
                   class="nav-link <?php echo $current_section === 'member_management' ? 'active' : ''; ?>">
                    <i class="bi bi-people"></i>
                    <span>Member Management</span>
                </a>
            </li>
            
            <!-- Recharge -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/recharge/" 
                   class="nav-link <?php echo $current_section === 'recharge' ? 'active' : ''; ?>">
                    <i class="bi bi-arrow-down-circle"></i>
                    <span>Recharge</span>
                    <?php 
                    if (tableExists('transactions')) {
                        $pending_recharge = getRecordCount('transactions', 'type = ? AND status = ?', ['deposit', 'pending']);
                        if ($pending_recharge > 0): 
                        ?>
                            <span class="badge bg-info rounded-pill ms-auto"><?php echo $pending_recharge; ?></span>
                        <?php endif;
                    } ?>
                </a>
            </li>
            
            <!-- Withdraw -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/withdraw/" 
                   class="nav-link <?php echo $current_section === 'withdraw' ? 'active' : ''; ?>">
                    <i class="bi bi-arrow-up-circle"></i>
                    <span>Withdraw</span>
                    <?php 
                    if (tableExists('transactions')) {
                        $pending_withdrawals = getRecordCount('transactions', 'type = ? AND status = ?', ['withdrawal', 'pending']);
                        if ($pending_withdrawals > 0): 
                        ?>
                            <span class="badge bg-danger rounded-pill ms-auto"><?php echo $pending_withdrawals; ?></span>
                        <?php endif;
                    } ?>
                </a>
            </li>
            
            <!-- Task Management -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/tasks/" 
                   class="nav-link <?php echo $current_section === 'tasks' ? 'active' : ''; ?>">
                    <i class="bi bi-list-task"></i>
                    <span>Task Management</span>
                </a>
                <ul class="nav flex-column ms-3">
                    <li class="nav-item">
                        <a href="<?php echo BASE_URL; ?>admin/tasks/assign.php" class="nav-link">Assign Task</a>
                    </li>
                </ul>
            </li>
            
            <!-- Product Management -->
            <li class="nav-item">
                <a href="#" class="nav-link" data-bs-toggle="collapse" data-bs-target="#product-menu">
                    <i class="bi bi-box-seam"></i>
                    <span>Product Management</span>
                    <i class="bi bi-chevron-down ms-auto"></i>
                </a>
                <div class="collapse <?php echo in_array($current_section, ['products', 'product_list']) ? 'show' : ''; ?>" id="product-menu">
                    <ul class="nav flex-column ms-3">
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>admin/products/" 
                               class="nav-link <?php echo $current_section === 'products' ? 'active' : ''; ?>">
                                <i class="bi bi-list-ul"></i>
                                <span>Product List</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>admin/products/add.php" 
                               class="nav-link <?php echo $current_page === 'add.php' ? 'active' : ''; ?>">
                                <i class="bi bi-plus-circle"></i>
                                <span>Add Product</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo BASE_URL; ?>admin/products/categories.php" 
                               class="nav-link <?php echo $current_page === 'categories.php' ? 'active' : ''; ?>">
                                <i class="bi bi-tags"></i>
                                <span>Categories</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </li>

            <!-- Database Backup -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/backup/" 
                   class="nav-link <?php echo $current_section === 'backup' ? 'active' : ''; ?>">
                    <i class="bi bi-database"></i>
                    <span>Database Backup</span>
                </a>
            </li>

            <!-- Payment Card Management -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/payment_card/" 
                   class="nav-link <?php echo $current_section === 'payment_card' ? 'active' : ''; ?>">
                    <i class="bi bi-credit-card"></i>
                    <span>Payment Card</span>
                </a>
            </li>

            <!-- Top-Up Orders -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/top_up_orders/" 
                   class="nav-link <?php echo $current_section === 'top_up_orders' ? 'active' : ''; ?>">
                    <i class="bi bi-cash-stack"></i>
                    <span>Top-Up Orders</span>
                </a>
            </li>

            <!-- Superior Management -->
            <li class="nav-item">
                <a href="<?php echo BASE_URL; ?>admin/superior_management/" 
                   class="nav-link <?php echo $current_section === 'superior_management' ? 'active' : ''; ?>">
                    <i class="bi bi-person-badge"></i>
                    <span>Superior Management</span>
                </a>
            </li>
            
        </ul>
    </nav>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="user-info">
            <div class="user-avatar">
                <i class="bi bi-person-circle"></i>
            </div>
            <div class="user-details">
                <div class="user-name"><?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></div>
                <div class="user-role"><?php echo htmlspecialchars($_SESSION['admin_role'] ?? 'Administrator'); ?></div>
            </div>
        </div>
        
        <div class="sidebar-actions">
            <a href="<?php echo BASE_URL; ?>" class="btn btn-outline-light btn-sm" title="View Site">
                <i class="bi bi-eye"></i>
            </a>
            <a href="<?php echo BASE_URL; ?>admin/logout/logout.php" class="btn btn-outline-light btn-sm" title="Logout">
                <i class="bi bi-box-arrow-right"></i>
            </a>
        </div>
    </div>
</div>
