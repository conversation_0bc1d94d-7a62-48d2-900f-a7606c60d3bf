<?php
/**
 * Quick Test Script for Bamboo Setup
 */

echo "<h2>🧪 Bamboo Quick Test</h2>";
echo "<p><strong>✅ PHP is working!</strong></p>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Time: " . date('Y-m-d H:i:s') . "</p>";

// Test if we can include config
try {
    define('BAMBOO_APP', true);
    require_once '../includes/config.php';
    echo "<p><strong>✅ Config loaded successfully!</strong></p>";
    echo "<p>Base URL: " . BASE_URL . "</p>";
    echo "<p>Debug Mode: " . (DEBUG_MODE ? 'ON' : 'OFF') . "</p>";
} catch (Exception $e) {
    echo "<p><strong>❌ Config error:</strong> " . $e->getMessage() . "</p>";
}

// Test database connection
try {
    require_once '../includes/functions.php';
    $db = getDB();
    echo "<p><strong>✅ Database connected successfully!</strong></p>";
    
    // Test a simple query
    $stmt = $db->query("SELECT COUNT(*) as count FROM vip_levels");
    $result = $stmt->fetch();
    echo "<p>VIP Levels in database: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p><strong>❌ Database error:</strong> " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='../'>← Back to Main App</a></p>";
echo "<p><a href='test_connection.php'>Full System Test →</a></p>";
?>