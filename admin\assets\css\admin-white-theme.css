/**
 * Bamboo Web Application - Admin White Theme Override
 * Company: Notepadsly
 * Version: 1.0
 */

/* Define shades of white for UI elements */
:root {
    --admin-bg-light: #f8f9fa; /* A very light grey, almost white */
    --admin-bg-white: #ffffff; /* Pure white */
    --admin-bg-dark-white: #e9ecef; /* A slightly darker shade of white/light grey */
    --admin-bg-light-white: #f1f3f5; /* A light grey for subtle contrast */
    --admin-text-dark: #212529; /* Dark text for readability */
    --admin-text-muted: #6c757d; /* Muted text color */
}

/* Apply base background for the admin page */
.admin-page {
    background-color: var(--admin-bg-light);
}

/* Apply background to main content areas */
.admin-main,
.admin-content {
    background-color: var(--admin-bg-light);
}

/* Apply varying shades of white to cards and related elements */
.card {
    background-color: var(--admin-bg-white); /* Cards default to pure white */
    border: 1px solid rgba(0, 0, 0, 0.125); /* Subtle border for definition */
}

.card-header {
    background-color: var(--admin-bg-light-white); /* Card headers are light white */
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.card-body {
    background-color: var(--admin-bg-white); /* Card bodies remain pure white */
}

.card-footer {
    background-color: var(--admin-bg-light-white); /* Card footers are light white */
    border-top: 1px solid rgba(0, 0, 0, 0.125);
}

/* Ensure other elements respect the new color scheme */
.admin-topbar,
.admin-footer,
.modal-content,
.dropdown-menu {
    background-color: var(--admin-bg-white);
}

/* Ensure form controls have appropriate background */
.form-control,
.form-select,
.input-group-text {
    background-color: var(--admin-bg-white);
}

/* Table styling for better integration */
.table {
    background-color: var(--admin-bg-white);
}
.table thead th {
    background: var(--admin-table-header-bg, var(--admin-primary)) !important;
    color: var(--admin-table-header-text, #ffffff) !important;
}
.table tbody td {
    background-color: var(--admin-bg-white);
}
.table-hover tbody tr:hover {
    background-color: var(--admin-bg-light);
}

/* Keep sidebar with its gradient background */
.admin-sidebar {
    /* Sidebar keeps its original styling */
}

/* Ensure proper contrast for text */
body, p, h1, h2, h3, h4, h5, h6, .card-title, .table {
    color: var(--admin-text-dark);
}
