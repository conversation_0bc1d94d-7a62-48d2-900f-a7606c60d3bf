/**
 * Bamboo Web Application - Dynamic Theme System
 * Company: Notepadsly
 * Version: 1.0
 * 
 * This file contains CSS that can be dynamically updated by the appearance settings
 */

/* Dynamic CSS Variables - These will be updated by JavaScript */
:root {
    /* Primary Colors */
    --dynamic-primary: var(--admin-primary, #ff6900);
    --dynamic-secondary: var(--admin-secondary, #ffffff);
    --dynamic-accent: var(--admin-accent, #007bff);

    /* Gradient Colors */
    --dynamic-gradient-start: var(--admin-primary, #ff6900);
    --dynamic-gradient-end: var(--admin-gradient-end, #ff8533);

    /* Background Colors */
    --dynamic-card-bg: #ffffff;
    --dynamic-sidebar-bg: linear-gradient(180deg, var(--dynamic-gradient-start) 0%, var(--dynamic-gradient-end) 100%);

    /* Shadow Settings */
    --dynamic-card-shadow: var(--admin-shadow-sm);
    --dynamic-border-radius: var(--admin-border-radius);

    /* Convert hex to RGB for alpha usage */
    --dynamic-primary-rgb: var(--admin-primary-rgb, 255, 105, 0);
    --dynamic-accent-rgb: var(--admin-accent-rgb, 0, 123, 255);
}

/* Dynamic Sidebar Styling */
.admin-sidebar {
    background: var(--dynamic-sidebar-bg) !important;
}

/* Dynamic Card Styling */
.card {
    background: var(--dynamic-card-bg) !important;
    box-shadow: var(--dynamic-card-shadow) !important;
    border-radius: var(--dynamic-border-radius) !important;
}

/* Dynamic Button Styling - Will be overridden by admin_header.php styles */
.btn-primary {
    border-color: var(--dynamic-primary) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--dynamic-primary-rgb), 0.3) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover {
    box-shadow: 0 0.5rem 1.5rem rgba(var(--dynamic-primary-rgb), 0.4) !important;
    transform: translateY(-1px) !important;
    opacity: 0.9 !important;
}

/* Single Color Button Variants */
.btn-primary-single {
    background: var(--dynamic-primary) !important;
    border-color: var(--dynamic-primary) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--dynamic-primary-rgb), 0.3) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.btn-primary-single:hover {
    background: rgba(var(--dynamic-primary-rgb), 0.9) !important;
    box-shadow: 0 0.5rem 1.5rem rgba(var(--dynamic-primary-rgb), 0.4) !important;
    transform: translateY(-1px) !important;
}

.btn-accent-single {
    background: var(--dynamic-accent) !important;
    border-color: var(--dynamic-accent) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--dynamic-accent-rgb), 0.3) !important;
    color: white !important;
    transition: all 0.3s ease !important;
}

.btn-accent-single:hover {
    background: rgba(var(--dynamic-accent-rgb), 0.9) !important;
    box-shadow: 0 0.5rem 1.5rem rgba(var(--dynamic-accent-rgb), 0.4) !important;
    transform: translateY(-1px) !important;
}

/* Dynamic Stat Card Icons */
.stat-icon {
    background: linear-gradient(135deg, var(--dynamic-primary), rgba(var(--dynamic-primary-rgb), 0.8)) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--dynamic-primary-rgb), 0.3) !important;
}

/* Dynamic Badge Styling */
.badge.bg-primary {
    background: linear-gradient(135deg, var(--dynamic-primary), var(--dynamic-accent)) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(var(--dynamic-primary-rgb), 0.3) !important;
}

/* Dynamic Navigation Active State */
.sidebar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2) !important;
    border-right: 3px solid var(--dynamic-secondary) !important;
}

/* Dynamic Gradient Preview */
.gradient-preview {
    background: linear-gradient(135deg, var(--dynamic-gradient-start) 0%, var(--dynamic-gradient-end) 100%) !important;
}

/* Dynamic Form Controls */
.form-control:focus, .form-select:focus {
    border-color: var(--dynamic-primary) !important;
    box-shadow: 0 0 0 0.2rem rgba(var(--dynamic-primary-rgb), 0.25) !important;
}

/* Dynamic Link Colors */
a {
    color: var(--dynamic-primary);
}

a:hover {
    color: var(--dynamic-accent);
}

/* Dynamic Text Selection */
::selection {
    background: rgba(var(--dynamic-primary-rgb), 0.2);
    color: var(--dynamic-primary);
}

/* Dynamic Scrollbar */
::-webkit-scrollbar-thumb {
    background: var(--dynamic-primary);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dynamic-accent);
}

/* Dynamic Loading Spinner */
.loading-spinner {
    border-top-color: var(--dynamic-primary) !important;
}

/* Dynamic Progress Bars */
.progress-bar {
    background: linear-gradient(135deg, var(--dynamic-primary), var(--dynamic-accent)) !important;
}

/* Dynamic Alert Styling */
.alert-primary {
    background: rgba(var(--dynamic-primary-rgb), 0.1) !important;
    border-color: rgba(var(--dynamic-primary-rgb), 0.2) !important;
    color: var(--dynamic-primary) !important;
}

/* Dynamic Table Styling */
.table-primary {
    background: rgba(var(--dynamic-primary-rgb), 0.05) !important;
}

.table-hover tbody tr:hover {
    background: rgba(var(--dynamic-primary-rgb), 0.03) !important;
}

/* Dynamic Modal Styling */
.modal-header {
    background: linear-gradient(135deg, var(--dynamic-primary), var(--dynamic-accent)) !important;
    color: white !important;
}

/* Dynamic Dropdown Styling */
.dropdown-item:hover, .dropdown-item:focus {
    background: rgba(var(--dynamic-primary-rgb), 0.1) !important;
    color: var(--dynamic-primary) !important;
}

/* Dynamic Pagination */
.page-link {
    color: var(--dynamic-primary) !important;
}

.page-item.active .page-link {
    background: var(--dynamic-primary) !important;
    border-color: var(--dynamic-primary) !important;
}

/* Dynamic Tooltip */
.tooltip-inner {
    background: var(--dynamic-primary) !important;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before {
    border-top-color: var(--dynamic-primary) !important;
}

.tooltip.bs-tooltip-bottom .tooltip-arrow::before {
    border-bottom-color: var(--dynamic-primary) !important;
}

.tooltip.bs-tooltip-start .tooltip-arrow::before {
    border-left-color: var(--dynamic-primary) !important;
}

.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-right-color: var(--dynamic-primary) !important;
}

/* Dynamic Popover */
.popover-header {
    background: var(--dynamic-primary) !important;
    color: white !important;
}

/* Dynamic Switch/Toggle */
.form-check-input:checked {
    background-color: var(--dynamic-primary) !important;
    border-color: var(--dynamic-primary) !important;
}

/* Dynamic Range Slider */
.form-range::-webkit-slider-thumb {
    background: var(--dynamic-primary) !important;
}

.form-range::-moz-range-thumb {
    background: var(--dynamic-primary) !important;
}

/* Dynamic Accordion */
.accordion-button:not(.collapsed) {
    background: rgba(var(--dynamic-primary-rgb), 0.1) !important;
    color: var(--dynamic-primary) !important;
}

/* Dynamic Breadcrumb */
.breadcrumb-item.active {
    color: var(--dynamic-primary) !important;
}

/* Dynamic List Group */
.list-group-item.active {
    background: var(--dynamic-primary) !important;
    border-color: var(--dynamic-primary) !important;
}

/* Dynamic Navbar */
.navbar-brand {
    color: var(--dynamic-primary) !important;
}

.navbar-nav .nav-link.active {
    color: var(--dynamic-primary) !important;
}

/* Dynamic Offcanvas */
.offcanvas-header {
    background: linear-gradient(135deg, var(--dynamic-primary), var(--dynamic-accent)) !important;
    color: white !important;
}

/* Dynamic Toast */
.toast-header {
    background: rgba(var(--dynamic-primary-rgb), 0.1) !important;
    color: var(--dynamic-primary) !important;
}

/* Dynamic Spinner */
.spinner-border {
    color: var(--dynamic-primary) !important;
}

.spinner-grow {
    color: var(--dynamic-primary) !important;
}

/* Dynamic Tab Navigation */
.nav-tabs .nav-link.active {
    color: var(--dynamic-primary) !important;
    border-bottom-color: var(--dynamic-primary) !important;
}

.nav-pills .nav-link.active {
    background: var(--dynamic-primary) !important;
}

/* Dynamic Card Hover Effects */
.card:hover {
    border-color: rgba(var(--dynamic-primary-rgb), 0.2) !important;
}

/* Dynamic Focus States */
.form-control:focus,
.form-select:focus,
.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--dynamic-primary-rgb), 0.25) !important;
}

/* Dynamic Text Colors */
.text-primary {
    color: var(--dynamic-primary) !important;
}

.bg-primary {
    background-color: var(--dynamic-primary) !important;
}

.border-primary {
    border-color: var(--dynamic-primary) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .admin-sidebar {
        background: var(--dynamic-sidebar-bg) !important;
    }
}

/* Print styles */
@media print {
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
