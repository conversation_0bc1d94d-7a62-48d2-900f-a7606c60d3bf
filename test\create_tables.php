<?php
define('BAMBOO_APP', true);
require_once dirname(__DIR__) . '/includes/config.php';
require_once dirname(__DIR__) . '/includes/database.php';

$pdo = getDB();

$queries = [
    "CREATE TABLE IF NOT EXISTS `negative_settings` ( `id` int(11) NOT NULL AUTO_INCREMENT, `user_id` int(11) NOT NULL, `trigger_task_number` int(11) NOT NULL, `product_id_override` int(11) NOT NULL, `override_amount` decimal(15,2) NOT NULL, `is_active` tinyint(1) NOT NULL DEFAULT 1, `is_triggered` tinyint(1) NOT NULL DEFAULT 0, `admin_id_created` int(11) NOT NULL, `created_at` datetime NOT NULL DEFAULT current_timestamp(), PRIMARY KEY (`id`), KEY `user_id` (`user_id`), KEY `product_id_override` (`product_id_override`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;",
    "CREATE TABLE IF NOT EXISTS `withdrawal_quotes` ( `id` int(11) NOT NULL AUTO_INCREMENT, `user_id` int(11) NOT NULL, `message` text NOT NULL, `status` enum('active','resolved') NOT NULL DEFAULT 'active', `admin_id_created` int(11) NOT NULL, `created_at` datetime NOT NULL DEFAULT current_timestamp(), PRIMARY KEY (`id`), KEY `user_id` (`user_id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;",
    "CREATE TABLE IF NOT EXISTS `user_salaries` ( `id` int(11) NOT NULL AUTO_INCREMENT, `user_id` int(11) NOT NULL, `amount` decimal(15,2) NOT NULL, `status` enum('paid','pending_approval') NOT NULL, `admin_id_processed` int(11) NOT NULL, `paid_at` datetime DEFAULT NULL, `notes` text DEFAULT NULL, `created_at` datetime NOT NULL DEFAULT current_timestamp(), PRIMARY KEY (`id`), KEY `user_id` (`user_id`) ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;"
];

foreach ($queries as $query) {
    try {
        $pdo->exec($query);
        echo "Table created successfully!\n";
    } catch (PDOException $e) {
        echo "Error creating table: " . $e->getMessage() . "\n";
    }
}
