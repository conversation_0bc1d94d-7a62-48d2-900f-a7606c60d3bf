<?php
/**
 * Database Reset Script for Testing
 * WARNING: This will delete all data!
 */

define('BAMBOO_APP', true);
require_once '../includes/config.php';

?>
<!DOCTYPE html>
<html>
<head>
    <title>Reset Database - Bamboo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ff6900 100%); min-height: 100vh; }
        .container { max-width: 600px; margin: 2rem auto; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h2><i class="bi bi-exclamation-triangle"></i> Database Reset</h2>
            </div>
            <div class="card-body">
                
                <?php if (isset($_POST['confirm_reset']) && $_POST['confirm_reset'] === 'YES_DELETE_ALL'): ?>
                    
                    <?php
                    try {
                        // Read and execute the migration script
                        $sql_file = '../database_migration.sql';
                        if (!file_exists($sql_file)) {
                            throw new Exception('Migration file not found');
                        }
                        
                        $sql_content = file_get_contents($sql_file);
                        
                        // Connect to database
                        $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";charset=utf8mb4";
                        $pdo = new PDO($dsn, DB_USER, DB_PASS);
                        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                        
                        // Split SQL into individual statements
                        $statements = array_filter(
                            array_map('trim', explode(';', $sql_content)),
                            function($stmt) {
                                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
                            }
                        );
                        
                        $executed = 0;
                        $errors = [];
                        
                        foreach ($statements as $statement) {
                            if (trim($statement)) {
                                try {
                                    $pdo->exec($statement);
                                    $executed++;
                                } catch (PDOException $e) {
                                    // Skip certain expected errors
                                    if (strpos($e->getMessage(), 'already exists') === false &&
                                        strpos($e->getMessage(), 'Unknown database') === false) {
                                        $errors[] = $e->getMessage();
                                    }
                                }
                            }
                        }
                        
                        echo '<div class="alert alert-success">';
                        echo '<h4>✅ Database Reset Complete!</h4>';
                        echo "<p>Executed {$executed} SQL statements successfully.</p>";
                        
                        if (!empty($errors)) {
                            echo '<h5>Non-critical errors:</h5>';
                            echo '<ul>';
                            foreach ($errors as $error) {
                                echo '<li>' . htmlspecialchars($error) . '</li>';
                            }
                            echo '</ul>';
                        }
                        
                        echo '</div>';
                        
                        echo '<div class="alert alert-info">';
                        echo '<h5>What was reset:</h5>';
                        echo '<ul>';
                        echo '<li>All tables dropped and recreated</li>';
                        echo '<li>Default VIP levels inserted</li>';
                        echo '<li>Default settings inserted</li>';
                        echo '<li>Sample admin user created</li>';
                        echo '<li>Database triggers and procedures created</li>';
                        echo '</ul>';
                        echo '</div>';
                        
                    } catch (Exception $e) {
                        echo '<div class="alert alert-danger">';
                        echo '<h4>❌ Reset Failed!</h4>';
                        echo '<p>Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
                        echo '</div>';
                    }
                    ?>
                    
                    <div class="mt-4">
                        <a href="../" class="btn btn-success">Test Application →</a>
                        <a href="database_check.php" class="btn btn-primary">Check Database</a>
                    </div>
                    
                <?php else: ?>
                    
                    <div class="alert alert-warning">
                        <h4>⚠️ WARNING</h4>
                        <p>This will completely reset the database by:</p>
                        <ul>
                            <li>Dropping all existing tables</li>
                            <li>Recreating the database structure</li>
                            <li>Inserting default data</li>
                            <li><strong>ALL EXISTING DATA WILL BE LOST!</strong></li>
                        </ul>
                    </div>
                    
                    <form method="POST">
                        <div class="mb-3">
                            <label class="form-label">Type <strong>YES_DELETE_ALL</strong> to confirm:</label>
                            <input type="text" 
                                   class="form-control" 
                                   name="confirm_reset" 
                                   placeholder="Type: YES_DELETE_ALL"
                                   required>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-danger btn-lg">
                                🗑️ RESET DATABASE
                            </button>
                            <a href="../" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                    
                <?php endif; ?>
                
            </div>
        </div>
    </div>
</body>
</html>