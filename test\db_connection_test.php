<?php
/**
 * Bamboo Web Application - DB Connection Test (CLI Version)
 * Company: Notepadsly
 * Version: 1.1
 */

// Define a base path for CLI execution
define('BAMBOO_APP', true);
define('BASE_PATH', dirname(__DIR__));

// Suppress web-related warnings for CLI
error_reporting(E_ALL & ~E_WARNING & ~E_DEPRECATED);

// Include only the necessary database file
require_once __DIR__ . 'includes/database.php';

echo "Attempting to connect to the database...\n";

try {
    // Get the PDO instance
    $pdo = getDB();
    
    // Check if the connection is valid
    if ($pdo) {
        echo "[SUCCESS] Database connection established successfully.\n";
        
        // Check for vip_levels table
        $stmt = $pdo->query("SHOW TABLES LIKE 'vip_levels'");
        if ($stmt->rowCount() > 0) {
            echo "[INFO] Table 'vip_levels' found.\n";
            
            // Check for icon_path column
            $stmt = $pdo->query("SHOW COLUMNS FROM `vip_levels` LIKE 'icon_path'");
            if ($stmt->rowCount() > 0) {
                echo "[SUCCESS] Column 'icon_path' exists in 'vip_levels' table.\n";
            } else {
                echo "[FAILURE] Column 'icon_path' is MISSING from 'vip_levels' table.\n";
                echo "[ACTION] Please run the following SQL command on your database:\n";
                echo "ALTER TABLE `vip_levels` ADD `icon_path` VARCHAR(255) NULL DEFAULT NULL AFTER `benefits`;\n";
            }
        } else {
            echo "[FAILURE] Table 'vip_levels' not found.\n";
        }

    } else {
        echo "[FAILURE] Failed to establish a database connection. Please check your credentials in config.php.\n";
    }
} catch (PDOException $e) {
    echo "[FAILURE] PDOException: " . $e->getMessage() . "\n";
    echo "[INFO] Please ensure your database server is running and the credentials in includes/config.php are correct.\n";
} catch (Exception $e) {
    echo "[FAILURE] An unexpected error occurred: " . $e->getMessage() . "\n";
}
