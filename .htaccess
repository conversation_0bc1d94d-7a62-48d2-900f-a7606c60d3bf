# Bamboo Web Application - URL Rewriting Rules
# Supports both localhost/bamboo/ and online hosting

RewriteEngine On

# Security - Block access to sensitive files
<Files "*.sql">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "config.php">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to .htaccess
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

# Block access to includes directory from direct access
RewriteRule ^includes/.*$ - [F,L]

# Block access to uploads directory scripts
RewriteRule ^uploads/.*\.(php|php3|php4|php5|phtml)$ - [F,L]

# Main routing - redirect everything to index.php
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/bamboo/assets/
RewriteCond %{REQUEST_URI} !^/assets/
RewriteRule ^(.*)$ index.php [QSA,L]

# Handle both localhost/bamboo/ and online hosting
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} ^/bamboo/
RewriteRule ^bamboo/(.*)$ index.php [QSA,L]

# Cache static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>