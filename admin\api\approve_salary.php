<?php
/**
 * Bamboo Web Application - Approve Salary API
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$salary_id = (int)($_POST['salary_id'] ?? 0);

if ($salary_id <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid salary ID.'], 400);
}

try {
    beginTransaction();

    // Get salary record
    $salary = fetchRow("SELECT user_id, amount, status FROM user_salaries WHERE id = ?", [$salary_id]);
    if (!$salary) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Salary record not found.'], 404);
    }

    if ($salary['status'] === 'paid') {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Salary already paid.'], 400);
    }

    // Update salary status
    $update_salary_success = updateRecord('user_salaries', ['status' => 'paid', 'paid_at' => date('Y-m-d H:i:s')], 'id = ?', [$salary_id]);

    if (!$update_salary_success) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Failed to update salary status.'], 500);
    }

    // Add amount to user's balance
    $user = fetchRow("SELECT id, balance FROM users WHERE id = ?", [$salary['user_id']]);
    $new_balance = $user['balance'] + $salary['amount'];
    $update_user_balance_success = updateRecord('users', ['balance' => $new_balance], 'id = ?', [$salary['user_id']]);

    if (!$update_user_balance_success) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Failed to update user balance.'], 500);
    }

    // Record transaction for salary payment
    $transaction_data = [
        'user_id' => $salary['user_id'],
        'type' => 'salary',
        'amount' => $salary['amount'],
        'status' => 'completed',
        'description' => 'Salary payment approved.',
        'admin_id_processed' => getCurrentAdminId()
    ];
    $transaction_id = insertRecord('transactions', $transaction_data);

    if (!$transaction_id) {
        rollbackTransaction();
        jsonResponse(['success' => false, 'message' => 'Failed to record transaction.'], 500);
    }

    commitTransaction();
    jsonResponse(['success' => true, 'message' => 'Salary approved successfully!']);

} catch (Exception $e) {
    rollbackTransaction();
    logError('Error approving salary: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>
