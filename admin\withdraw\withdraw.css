/**
 * Bamboo Web Application - Withdraw Management Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== WITHDRAW MANAGEMENT STYLES ===== */

/* Page Header */
.withdraw-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(0, 0, 0, 0.04);
}

.withdraw-header h1 {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, var(--admin-text-dark), rgba(44, 62, 80, 0.8));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Withdraw Card */
.withdraw-card {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #ffffff;
    overflow: hidden;
}

.withdraw-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 2rem rgba(0, 0, 0, 0.12);
}

.withdraw-card .card-header {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f3f4 100%);
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    padding: 1.25rem 1.5rem;
    position: relative;
}

.withdraw-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent);
}

.withdraw-card .card-body {
    padding: 0;
}

/* Filter Form */
.filter-form {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    flex-wrap: wrap;
}

.filter-form .form-control,
.filter-form .form-select {
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    min-width: 150px;
}

.filter-form .form-control:focus,
.filter-form .form-select:focus {
    border-color: var(--admin-primary);
    box-shadow: 0 0 0 0.2rem rgba(var(--admin-primary-rgb), 0.25);
}

.filter-form .btn {
    border-radius: 0.5rem;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

/* Modern Table Styling */
.withdraw-table {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
    font-size: 0.875rem;
}

.withdraw-table thead th {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: none;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: var(--admin-text-dark);
    text-transform: uppercase;
    letter-spacing: 0.025em;
    font-size: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.withdraw-table thead th:first-child {
    border-top-left-radius: 0.75rem;
}

.withdraw-table thead th:last-child {
    border-top-right-radius: 0.75rem;
}

.withdraw-table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(0, 0, 0, 0.04);
}

.withdraw-table tbody tr:hover {
    background: linear-gradient(135deg, #fafbfc 0%, #f8f9fa 100%);
    transform: scale(1.01);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.08);
}

.withdraw-table tbody tr:last-child {
    border-bottom: none;
}

.withdraw-table tbody td {
    padding: 1rem 0.75rem;
    border: none;
    vertical-align: middle;
    line-height: 1.4;
}

/* User Info Styling */
.user-info {
    font-size: 0.875rem;
    line-height: 1.3;
}

.user-info .username {
    font-weight: 600;
    color: var(--admin-text-dark);
    margin-bottom: 0.25rem;
}

.user-info .details {
    color: var(--admin-text-muted);
    font-size: 0.75rem;
}

/* Transaction ID */
.transaction-id {
    font-family: 'Courier New', monospace;
    font-size: 0.75rem;
    background: rgba(0, 0, 0, 0.05);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    word-break: break-all;
}

/* Amount Styling */
.withdraw-amount {
    font-weight: 700;
    color: #dc3545;
    font-size: 0.9rem;
}

.withdraw-amount::before {
    content: '-';
    margin-right: 0.125rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    border: none;
}

.status-badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(245, 158, 11, 0.3);
    color: white !important;
}

.status-badge.bg-success {
    background: linear-gradient(135deg, #10b981, #059669) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(16, 185, 129, 0.3);
}

.status-badge.bg-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626) !important;
    box-shadow: 0 0.25rem 0.75rem rgba(239, 68, 68, 0.3);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-buttons .btn {
    border-radius: 0.5rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    position: relative;
    overflow: hidden;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-buttons .btn:hover::before {
    left: 100%;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
}

.action-buttons .btn-success {
    background: linear-gradient(135deg, #10b981, #059669);
    box-shadow: 0 0.25rem 0.75rem rgba(16, 185, 129, 0.3);
}

.action-buttons .btn-success:hover {
    box-shadow: 0 0.5rem 1.5rem rgba(16, 185, 129, 0.4);
}

.action-buttons .btn-danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    box-shadow: 0 0.25rem 0.75rem rgba(239, 68, 68, 0.3);
}

.action-buttons .btn-danger:hover {
    box-shadow: 0 0.5rem 1.5rem rgba(239, 68, 68, 0.4);
}

/* Date Styling */
.transaction-date {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    white-space: nowrap;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--admin-text-muted);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    color: var(--admin-text-dark);
    margin-bottom: 0.5rem;
}

/* Pagination Enhancement */
.pagination-enhanced {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 0, 0, 0.04);
}

.pagination-enhanced .pagination {
    margin: 0;
    justify-content: center;
}

.pagination-enhanced .page-link {
    border: 2px solid rgba(0, 0, 0, 0.06);
    border-radius: 0.5rem;
    margin: 0 0.125rem;
    padding: 0.5rem 0.75rem;
    color: var(--admin-text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
}

.pagination-enhanced .page-link:hover {
    background: var(--admin-primary);
    border-color: var(--admin-primary);
    color: white;
    transform: translateY(-1px);
}

.pagination-enhanced .page-item.active .page-link {
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    border-color: var(--admin-primary);
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .withdraw-table {
        font-size: 0.8rem;
    }
    
    .withdraw-table thead th,
    .withdraw-table tbody td {
        padding: 0.75rem 0.5rem;
    }
}

@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-form .form-control,
    .filter-form .form-select {
        width: 100%;
        min-width: auto;
    }
    
    .withdraw-table {
        font-size: 0.75rem;
    }
    
    .withdraw-table thead th,
    .withdraw-table tbody td {
        padding: 0.5rem 0.25rem;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .action-buttons .btn {
        width: 100%;
        font-size: 0.7rem;
    }
}

/* Loading States */
.table-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
