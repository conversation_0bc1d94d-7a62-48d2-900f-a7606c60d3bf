<?php

define('BAMBOO_APP', true);

require_once __DIR__ . '/../includes/config.php';
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/functions.php';

// --- Test Setup ---

// Function to create a dummy user and related data for testing
function createDummyUser($username, $password) {
    $db = getDB();
    
    // Create user
    $password_hash = hashPassword($password);
    $user_data = [
        'username' => $username,
        'phone' => '12345' . uniqid(),
        'email' => $username . '@example.com',
        'password_hash' => $password_hash,
        'withdrawal_pin_hash' => 'dummy_pin_hash',
        'gender' => 'male',
        'invitation_code' => 'INV' . substr(uniqid(), -5),
        'invited_by' => null,
        'balance' => 100.00,
        'commission_balance' => 0.00,
        'frozen_balance' => 0.00,
        'total_deposited' => 0.00,
        'total_withdrawn' => 0.00,
        'total_commission_earned' => 0.00,
        'vip_level' => 1,
        'tasks_completed_today' => 0,
        'last_task_date' => null,
        'status' => 'active',
        'email_verified' => false,
        'phone_verified' => false,
        'avatar_url' => null,
        'referral_count' => 0,
        'last_login' => null,
        'login_count' => 0,
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ];
    $user_id = insertRecord('users', $user_data);

    if (!$user_id) {
        echo "Failed to create dummy user.\n";
        return false;
    }

    // Add some related data
    insertRecord('transactions', [
        'user_id' => $user_id,
        'type' => 'deposit',
        'amount' => 50.00,
        'balance_before' => 0.00,
        'balance_after' => 50.00,
        'status' => 'completed',
        'description' => 'Test deposit',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    insertRecord('tasks', [
        'user_id' => $user_id,
        'title' => 'Test Task',
        'description' => 'Description for test task',
        'status' => 'pending',
        'created_at' => date('Y-m-d H:i:s'),
        'updated_at' => date('Y-m-d H:i:s')
    ]);

    // Simulate a user session
    insertRecord('user_sessions', [
        'user_id' => $user_id,
        'session_id' => 'test_session_' . uniqid(),
        'login_time' => date('Y-m-d H:i:s'),
        'ip_address' => '127.0.0.1',
        'user_agent' => 'Test Agent'
    ]);

    // Add conditional table data if tables exist
    if (tableExists('user_salaries')) {
        insertRecord('user_salaries', [
            'user_id' => $user_id,
            'amount' => 1000.00,
            'pay_date' => date('Y-m-d H:i:s'),
            'status' => 'paid'
        ]);
    }
    if (tableExists('withdrawal_quotes')) {
        insertRecord('withdrawal_quotes', [
            'user_id' => $user_id,
            'amount' => 20.00,
            'status' => 'pending',
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    if (tableExists('negative_settings')) {
        insertRecord('negative_settings', [
            'user_id' => $user_id,
            'setting_key' => 'test_key',
            'setting_value' => 'test_value'
        ]);
    }
    insertRecord('notifications', [
        'user_id' => $user_id,
        'target_user_id' => $user_id,
        'type' => 'test',
        'message' => 'Test notification',
        'is_read' => 0,
        'created_at' => date('Y-m-d H:i:s')
    ]);

    echo "Dummy user '{$username}' created with ID: {$user_id}\n";
    return $user_id;
}

// Function to verify deletion
function verifyUserDeletion($user_id) {
    echo "Verifying deletion for user ID: {$user_id}\n";
    $db = getDB();

    $user_exists = recordExists('users', 'id = ?', [$user_id]);
    echo "User exists: " . ($user_exists ? 'Yes' : 'No') . "\n";

    $transactions_exist = recordExists('transactions', 'user_id = ?', [$user_id]);
    echo "Transactions exist: " . ($transactions_exist ? 'Yes' : 'No') . "\n";

    $tasks_exist = recordExists('tasks', 'user_id = ?', [$user_id]);
    echo "Tasks exist: " . ($tasks_exist ? 'Yes' : 'No') . "\n";

    $sessions_exist = recordExists('user_sessions', 'user_id = ?', [$user_id]);
    echo "Sessions exist: " . ($sessions_exist ? 'Yes' : 'No') . "\n";

    $notifications_exist = recordExists('notifications', 'target_user_id = ?', [$user_id]);
    echo "Notifications exist: " . ($notifications_exist ? 'Yes' : 'No') . "\n";

    $all_deleted = !$user_exists && !$transactions_exist && !$tasks_exist && !$sessions_exist && !$notifications_exist;

    // Check conditional tables
    if (tableExists('user_salaries')) {
        $salaries_exist = recordExists('user_salaries', 'user_id = ?', [$user_id]);
        echo "User salaries exist: " . ($salaries_exist ? 'Yes' : 'No') . "\n";
        $all_deleted = $all_deleted && !$salaries_exist;
    }
    if (tableExists('withdrawal_quotes')) {
        $quotes_exist = recordExists('withdrawal_quotes', 'user_id = ?', [$user_id]);
        echo "Withdrawal quotes exist: " . ($quotes_exist ? 'Yes' : 'No') . "\n";
        $all_deleted = $all_deleted && !$quotes_exist;
    }
    if (tableExists('negative_settings')) {
        $negative_settings_exist = recordExists('negative_settings', 'user_id = ?', [$user_id]);
        echo "Negative settings exist: " . ($negative_settings_exist ? 'Yes' : 'No') . "\n";
        $all_deleted = $all_deleted && !$negative_settings_exist;
    }

    return $all_deleted;
}

// --- Test Execution ---

echo "\n--- Starting User Deletion Test ---\n";

// 1. Create a dummy user
$test_username = 'testuser_' . uniqid();
$test_password = 'password123';
$user_id_to_delete = createDummyUser($test_username, $test_password);

if ($user_id_to_delete) {
    // 2. Simulate deletion via the delete.php script
    // We'll simulate the POST request to delete.php
    echo "\nSimulating deletion of user ID: {$user_id_to_delete} via admin/member_management/delete.php\n";

    // Capture output of delete.php to avoid premature exit
    ob_start();
    
    // Set up $_GET and $_POST for the simulated request
    $_GET['id'] = $user_id_to_delete;
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_POST['confirm_delete'] = '1';
    $_SESSION['csrf_token'] = generateCSRFToken(); // Generate a valid CSRF token for the test
    $_POST['csrf_token'] = $_SESSION['csrf_token'];

    // Include the delete script. It will handle the deletion and redirect.
    // We need to prevent the actual redirect for the test to continue.
    // This is a hack for testing, in a real scenario you'd use a testing framework.
    $original_redirect_function = get_defined_functions()['user'][0] ?? null; // Store original redirect function if it exists
    
    // Temporarily redefine redirect to prevent script exit
    function redirect($url, $permanent = false) {
        echo "Redirect suppressed for testing: " . $url . "\n";
    }

    include __DIR__ . '/../admin/member_management/delete.php';
    
    // Restore original redirect function if it was defined
    if ($original_redirect_function) {
        rename_function('redirect', $original_redirect_function);
    }

    ob_end_clean(); // Discard output of delete.php

    // 3. Verify deletion
    if (verifyUserDeletion($user_id_to_delete)) {
        echo "\nTest Result: SUCCESS! User and all related records were deleted.\n";
    } else {
        echo "\nTest Result: FAILED! User or some related records still exist.\n";
    }
} else {
    echo "\nTest aborted: Failed to create dummy user.\n";
}

echo "\n--- User Deletion Test Finished ---\n";

?>