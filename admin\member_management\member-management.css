/**
 * Bamboo Web Application - Member Management Styles
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== MEMBER MANAGEMENT STYLES ===== */

/* Member Info with Avatar */
.member-info-with-avatar {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.member-avatar {
    flex-shrink: 0;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.avatar-sm:hover {
    transform: scale(1.1);
    border-color: var(--admin-primary);
}

.avatar-initials-sm {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    border: 2px solid rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.avatar-initials-sm:hover {
    transform: scale(1.1);
    box-shadow: 0 0.25rem 0.75rem rgba(var(--admin-primary-rgb), 0.4);
}

.member-details {
    flex: 1;
    min-width: 0;
}

.member-details .username {
    font-weight: 600;
    color: var(--admin-text-dark);
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
}

.member-details .member-id {
    font-size: 0.75rem;
    color: var(--admin-text-muted);
    font-weight: 500;
}

/* Contact Info */
.contact-info {
    font-size: 0.875rem;
    line-height: 1.3;
}

.contact-info .email {
    color: var(--admin-text-dark);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.contact-info .phone {
    color: var(--admin-text-muted);
    font-size: 0.8rem;
}

/* Task Statistics */
.task-stats {
    font-size: 0.8rem;
    line-height: 1.4;
}

.task-today,
.task-total {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
}

.task-today:last-child,
.task-total:last-child {
    margin-bottom: 0;
}

.task-stats .badge {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    font-weight: 600;
}

.task-stats small {
    min-width: 35px;
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.action-buttons .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.8rem;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.action-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.action-buttons .btn:hover::before {
    left: 100%;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
}

.action-buttons .btn-outline-primary:hover {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
    border-color: #4f46e5;
    box-shadow: 0 0.25rem 0.75rem rgba(79, 70, 229, 0.3);
}

.action-buttons .btn-outline-secondary:hover {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    border-color: #6c757d;
    box-shadow: 0 0.25rem 0.75rem rgba(108, 117, 125, 0.3);
}

.action-buttons .btn-outline-danger:hover {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
    box-shadow: 0 0.25rem 0.75rem rgba(220, 53, 69, 0.3);
}

/* Enhanced Table Styling */
.table {
    font-size: 0.875rem;
    border-collapse: separate;
    border-spacing: 0;
    border-radius: 0.75rem;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.5rem rgba(0, 0, 0, 0.08);
}

.table thead th {
    background: var(--admin-table-header-bg, var(--admin-primary, #ff6900)) !important;
    border: none;
    border-bottom: 3px solid rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3);
    padding: 1.25rem 0.75rem;
    font-weight: 600;
    color: white !important;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: all 0.3s ease;
    border-bottom: 2px solid #e9ecef !important;
    position: relative;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(248, 249, 250, 0.5);
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.05) 0%, rgba(var(--admin-primary-rgb, 255, 105, 0), 0.02) 100%);
    transform: scale(1.002);
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.12);
    border-bottom-color: rgba(var(--admin-primary-rgb, 255, 105, 0), 0.3) !important;
}

.table tbody tr:last-child {
    border-bottom: 2px solid #e9ecef !important;
}

/* Row Separator Enhancement */
.table tbody tr::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #dee2e6 20%, #dee2e6 80%, transparent 100%);
}

.table tbody tr:last-child::after {
    display: none;
}

.table tbody td {
    padding: 1.25rem 0.75rem;
    border: none;
    vertical-align: middle;
    line-height: 1.4;
    position: relative;
}

.table th {
    border-left: none;
    border-right: none;
}

/* Badge Enhancements */
.badge {
    font-weight: 600;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8, #138496) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(23, 162, 184, 0.3);
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--admin-primary), rgba(var(--admin-primary-rgb), 0.8)) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(var(--admin-primary-rgb), 0.3);
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(108, 117, 125, 0.3);
}

.badge.bg-success {
    background: linear-gradient(135deg, #28a745, #218838) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(40, 167, 69, 0.3);
}

.badge.bg-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(255, 193, 7, 0.3);
    color: #212529 !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #dc3545, #c82333) !important;
    box-shadow: 0 0.125rem 0.25rem rgba(220, 53, 69, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .member-info-with-avatar {
        gap: 0.5rem;
    }
    
    .avatar-sm,
    .avatar-initials-sm {
        width: 35px;
        height: 35px;
    }
    
    .member-details .username {
        font-size: 0.875rem;
    }
    
    .contact-info {
        font-size: 0.8rem;
    }
    
    .task-stats {
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .table {
        font-size: 0.8rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.75rem 0.5rem;
    }
    
    .member-info-with-avatar {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .avatar-sm,
    .avatar-initials-sm {
        width: 30px;
        height: 30px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .action-buttons .btn {
        width: 100%;
        font-size: 0.75rem;
    }
    
    .task-stats {
        font-size: 0.7rem;
    }
    
    .task-today,
    .task-total {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
}

/* Loading States */
.table-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid var(--admin-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
