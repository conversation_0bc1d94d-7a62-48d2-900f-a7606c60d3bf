<?php
/**
 * Bamboo Web Application - Test Member Management Index
 * Company: Notepadsly
 * Version: 1.0
 */

// Define BAMBOO_APP if not already defined (e.g., by config.php)
if (!defined('BAMBOO_APP')) {
    define('BAMBOO_APP', true);
}

// Store current directory
$current_dir = getcwd();

// Change directory to simulate being in admin/member_management/
chdir(dirname(__DIR__) . '/admin/member_management/');

require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Simulate admin login
$_SESSION['admin_logged_in'] = true;
$_SESSION['admin_username'] = 'test_admin';

// Capture output
ob_start();
include 'index.php'; // Include relative to the new current directory
$output = ob_get_clean();

// Change back to original directory
chdir($current_dir);

// Basic checks
if (strpos($output, 'Member Management') === false) {
    die('Test Failed: Member Management title not found.');
}

if (strpos($output, '<table class="table table-hover">') === false) {
    die('Test Failed: User table not found.');
}

if (strpos($output, 'Add New Member') === false) {
    die("Test Failed: 'Add New Member' button not found.");
}

echo "Test Passed: Member Management index page loaded successfully.";
