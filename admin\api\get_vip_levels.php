<?php
/**
 * Bamboo Web Application - Get VIP Levels API
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

try {
    $vip_levels = fetchAll("SELECT level, name FROM vip_levels ORDER BY level ASC");

    jsonResponse(['success' => true, 'vip_levels' => $vip_levels]);

} catch (Exception $e) {
    logError('Error fetching VIP levels: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>