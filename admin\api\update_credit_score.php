<?php
/**
 * Bamboo Web Application - API for updating user credit score
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Check for POST request
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Verify CSRF token
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    jsonResponse(['success' => false, 'message' => 'Invalid CSRF token.'], 403);
}

$user_id = (int)($_POST['user_id'] ?? 0);
$score = (int)($_POST['score'] ?? 0);

if ($user_id <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid user ID.'], 400);
}

if ($score < 0 || $score > 100) { // Assuming credit score is a percentage from 0-100
    jsonResponse(['success' => false, 'message' => 'Credit score must be between 0 and 100.'], 400);
}

// Update credit score using the new function
if (updateCreditScore($user_id, $score)) {
    jsonResponse(['success' => true, 'message' => 'Credit score updated successfully.']);
} else {
    jsonResponse(['success' => false, 'message' => 'Failed to update credit score.'], 500);
}
?>
