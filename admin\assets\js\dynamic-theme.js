/**
 * Bamboo Web Application - Dynamic Theme System
 * Company: Notepadsly
 * Version: 1.0
 */

class DynamicTheme {
    constructor() {
        this.root = document.documentElement;
        this.init();
    }

    init() {
        // Load saved theme settings
        this.loadThemeSettings();
        
        // Apply theme on page load
        this.applyTheme();
        
        // Listen for theme changes
        this.setupThemeListeners();
    }

    loadThemeSettings() {
        // Try to load from AdminApp settings first (from database)
        if (window.AdminApp && window.AdminApp.appearanceSettings) {
            this.settings = { ...window.AdminApp.appearanceSettings };
        } else {
            // Try to load from localStorage as fallback
            const savedSettings = localStorage.getItem('bamboo_theme_settings');

            if (savedSettings) {
                this.settings = JSON.parse(savedSettings);
            } else {
                // Default settings
                this.settings = {
                    primaryColor: '#ff6900',
                    secondaryColor: '#ffffff',
                    accentColor: '#007bff',
                    gradientStart: '#ff6900',
                    gradientEnd: '#ff8533',
                    cardBackground: '#ffffff',
                    sidebarStyle: 'gradient',
                    sidebarTextColor: '#ffffff',
                    cardShadow: 'subtle',
                    borderRadius: '1rem',
                    buttonStyle: 'gradient',
                    themeMode: 'light',
                    logoSize: 'medium',
                    tableHeaderColor: 'primary',
                    levelBadgeColor: 'primary',
                    footerBgColor: '#f8f9fa',
                    footerTextColor: '#6c757d'
                };
            }
        }
    }

    saveThemeSettings() {
        localStorage.setItem('bamboo_theme_settings', JSON.stringify(this.settings));
    }

    updateSetting(key, value) {
        this.settings[key] = value;
        this.saveThemeSettings();
        this.applyTheme();
    }

    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveThemeSettings();
        this.applyTheme();
    }

    applyTheme() {
        // Convert hex to RGB
        const primaryRgb = this.hexToRgb(this.settings.primaryColor);
        const accentRgb = this.hexToRgb(this.settings.accentColor);

        // Update CSS variables
        this.root.style.setProperty('--dynamic-primary', this.settings.primaryColor);
        this.root.style.setProperty('--dynamic-secondary', this.settings.secondaryColor);
        this.root.style.setProperty('--dynamic-accent', this.settings.accentColor);
        this.root.style.setProperty('--dynamic-gradient-start', this.settings.gradientStart);
        this.root.style.setProperty('--dynamic-gradient-end', this.settings.gradientEnd);
        this.root.style.setProperty('--dynamic-card-bg', this.settings.cardBackground);
        this.root.style.setProperty('--dynamic-border-radius', this.settings.borderRadius);
        this.root.style.setProperty('--admin-logo-size', this.getLogoSize(this.settings.logoSize));
        this.root.style.setProperty('--admin-table-header-bg', this.getTableHeaderColor(this.settings.tableHeaderColor));
        this.root.style.setProperty('--admin-level-badge-bg', this.getLevelBadgeColor(this.settings.levelBadgeColor));
        this.root.style.setProperty('--admin-footer-bg', this.settings.footerBgColor);
        this.root.style.setProperty('--admin-footer-text', this.settings.footerTextColor);
        
        // Update RGB values for alpha usage
        if (primaryRgb) {
            this.root.style.setProperty('--dynamic-primary-rgb', `${primaryRgb.r}, ${primaryRgb.g}, ${primaryRgb.b}`);
        }
        if (accentRgb) {
            this.root.style.setProperty('--dynamic-accent-rgb', `${accentRgb.r}, ${accentRgb.g}, ${accentRgb.b}`);
        }

        // Apply sidebar style
        this.applySidebarStyle();
        
        // Apply card shadow
        this.applyCardShadow();
        
        // Apply theme mode
        this.applyThemeMode();
    }

    applySidebarStyle() {
        const sidebar = document.querySelector('.admin-sidebar');
        if (!sidebar) return;

        switch (this.settings.sidebarStyle) {
            case 'gradient':
                this.root.style.setProperty('--dynamic-sidebar-bg', 
                    `linear-gradient(180deg, ${this.settings.gradientStart} 0%, ${this.settings.gradientEnd} 100%)`);
                sidebar.style.color = 'white';
                break;
            case 'solid':
                this.root.style.setProperty('--dynamic-sidebar-bg', this.settings.primaryColor);
                sidebar.style.color = 'white';
                break;
            case 'light':
                this.root.style.setProperty('--dynamic-sidebar-bg', '#f8f9fa');
                sidebar.style.color = '#495057';
                break;
        }
    }

    applyCardShadow() {
        let shadowValue;
        switch (this.settings.cardShadow) {
            case 'none':
                shadowValue = 'none';
                break;
            case 'subtle':
                shadowValue = '0 0.125rem 0.5rem rgba(0, 0, 0, 0.08)';
                break;
            case 'medium':
                shadowValue = '0 0.75rem 2rem rgba(0, 0, 0, 0.12)';
                break;
            case 'strong':
                shadowValue = '0 1.5rem 3rem rgba(0, 0, 0, 0.15)';
                break;
            default:
                shadowValue = '0 0.125rem 0.5rem rgba(0, 0, 0, 0.08)';
        }
        this.root.style.setProperty('--dynamic-card-shadow', shadowValue);
    }

    applyThemeMode() {
        const body = document.body;
        
        switch (this.settings.themeMode) {
            case 'dark':
                body.classList.add('theme-dark');
                body.classList.remove('theme-light');
                break;
            case 'light':
                body.classList.add('theme-light');
                body.classList.remove('theme-dark');
                break;
            case 'auto':
                // Use system preference
                const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
                if (prefersDark) {
                    body.classList.add('theme-dark');
                    body.classList.remove('theme-light');
                } else {
                    body.classList.add('theme-light');
                    body.classList.remove('theme-dark');
                }
                break;
        }
    }

    setupThemeListeners() {
        // Listen for system theme changes
        if (window.matchMedia) {
            window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
                if (this.settings.themeMode === 'auto') {
                    this.applyThemeMode();
                }
            });
        }

        // Listen for custom theme events
        document.addEventListener('themeUpdate', (e) => {
            if (e.detail) {
                this.updateSettings(e.detail);
            }
        });
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    rgbToHex(r, g, b) {
        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    getLogoSize(sizeType) {
        switch (sizeType) {
            case 'small': return '60px';
            case 'medium': return '80px';
            case 'large': return '120px';
            case 'extra-large': return '150px';
            default: return '80px';
        }
    }

    getTableHeaderColor(colorType) {
        switch (colorType) {
            case 'primary': return this.settings.primaryColor;
            case 'accent': return this.settings.accentColor;
            case 'secondary': return this.settings.secondaryColor;
            case 'gradient': return `linear-gradient(135deg, ${this.settings.gradientStart} 0%, ${this.settings.gradientEnd} 100%)`;
            default: return this.settings.primaryColor;
        }
    }

    getLevelBadgeColor(colorType) {
        switch (colorType) {
            case 'primary': return this.settings.primaryColor;
            case 'accent': return this.settings.accentColor;
            default: return this.settings.primaryColor;
        }
    }

    // Utility methods for external use
    getCurrentSettings() {
        return { ...this.settings };
    }

    resetToDefault() {
        this.settings = {
            primaryColor: '#ff6900',
            secondaryColor: '#ffffff',
            accentColor: '#007bff',
            gradientStart: '#ff6900',
            gradientEnd: '#ff8533',
            cardBackground: '#ffffff',
            sidebarStyle: 'gradient',
            cardShadow: 'subtle',
            borderRadius: '1rem',
            themeMode: 'light',
            logoSize: 'medium',
            tableHeaderColor: 'primary',
            levelBadgeColor: 'primary'
        };
        this.saveThemeSettings();
        this.applyTheme();
    }

    exportTheme() {
        return JSON.stringify(this.settings, null, 2);
    }

    importTheme(themeJson) {
        try {
            const importedSettings = JSON.parse(themeJson);
            this.updateSettings(importedSettings);
            return true;
        } catch (error) {
            console.error('Failed to import theme:', error);
            return false;
        }
    }

    // Predefined color palettes
    applyColorPalette(paletteName) {
        const palettes = {
            default: {
                primaryColor: '#ff6900',
                accentColor: '#007bff',
                gradientStart: '#ff6900',
                gradientEnd: '#ff8533'
            },
            blue: {
                primaryColor: '#007bff',
                accentColor: '#0056b3',
                gradientStart: '#007bff',
                gradientEnd: '#0056b3'
            },
            green: {
                primaryColor: '#28a745',
                accentColor: '#1e7e34',
                gradientStart: '#28a745',
                gradientEnd: '#1e7e34'
            },
            purple: {
                primaryColor: '#6f42c1',
                accentColor: '#5a32a3',
                gradientStart: '#6f42c1',
                gradientEnd: '#5a32a3'
            },
            red: {
                primaryColor: '#dc3545',
                accentColor: '#bd2130',
                gradientStart: '#dc3545',
                gradientEnd: '#bd2130'
            },
            orange: {
                primaryColor: '#fd7e14',
                accentColor: '#e55a00',
                gradientStart: '#fd7e14',
                gradientEnd: '#e55a00'
            }
        };

        if (palettes[paletteName]) {
            this.updateSettings(palettes[paletteName]);
        }
    }

    // Animation utilities
    animateColorTransition(fromColor, toColor, duration = 1000) {
        const start = performance.now();
        const fromRgb = this.hexToRgb(fromColor);
        const toRgb = this.hexToRgb(toColor);

        if (!fromRgb || !toRgb) return;

        const animate = (currentTime) => {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);

            const currentR = Math.round(fromRgb.r + (toRgb.r - fromRgb.r) * easeOut);
            const currentG = Math.round(fromRgb.g + (toRgb.g - fromRgb.g) * easeOut);
            const currentB = Math.round(fromRgb.b + (toRgb.b - fromRgb.b) * easeOut);

            const currentColor = this.rgbToHex(currentR, currentG, currentB);
            this.root.style.setProperty('--dynamic-primary', currentColor);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Update the actual setting when animation is complete
                this.updateSetting('primaryColor', toColor);
            }
        };

        requestAnimationFrame(animate);
    }
}

// Initialize the dynamic theme system
let dynamicTheme;

document.addEventListener('DOMContentLoaded', function() {
    // Wait a bit for AdminApp to be initialized
    setTimeout(function() {
        dynamicTheme = new DynamicTheme();

        // Make it globally available
        window.BambooDynamicTheme = dynamicTheme;
    }, 100);
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DynamicTheme;
}
