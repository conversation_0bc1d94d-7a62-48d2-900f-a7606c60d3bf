/**
 * Bamboo Web Application - Main CSS
 * Company: Notepadsly
 * Version: 1.0
 */

/* ===== ROOT VARIABLES ===== */
:root {
    --primary-color: #ff6900;
    --primary-dark: #e55a00;
    --primary-light: #ff8533;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #ffffff;
    --dark-color: #2c3e50;
    --gray-100: #f8f9fa;
    --gray-200: #e9ecef;
    --gray-300: #dee2e6;
    
    --gradient-primary: linear-gradient(135deg, #ff6900 0%, #ff8533 100%);
    --gradient-success: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    --gradient-danger: linear-gradient(135deg, #dc3545 0%, #fd7e14 100%);
    
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    --font-family-sans-serif: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-size-base: 1rem;
    --line-height-base: 1.5;
    
    --transition-base: all 0.3s ease;
    --animation-duration: 0.3s;
}

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family-sans-serif);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--dark-color);
    background-color: var(--light-color);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* ===== LOADING SPINNER ===== */
.loading-spinner {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-base);
}

.loading-spinner.show {
    opacity: 1;
    visibility: visible;
}

/* ===== FLASH MESSAGES ===== */
.flash-messages {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    max-width: 400px;
}

.flash-messages .alert {
    margin-bottom: 10px;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

/* ===== MAIN CONTAINER ===== */
.main-container {
    min-height: 100vh;
    padding: 0;
}

/* ===== AUTHENTICATION PAGES ===== */
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--light-color) 0%, var(--gray-100) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.auth-card {
    background: var(--light-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    border: 2px solid var(--gray-200);
}

.auth-logo {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo img {
    max-width: 120px;
    height: auto;
}

.auth-logo h1 {
    color: var(--primary-color);
    font-weight: bold;
    margin-top: 1rem;
}

/* ===== FORM STYLES ===== */
.form-control {
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    transition: var(--transition-base);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.input-group {
    margin-bottom: 1rem;
}

.input-group-text {
    background-color: var(--light-color);
    border-color: #ddd;
    color: var(--secondary-color);
}

/* ===== BUTTON STYLES ===== */
.btn {
    border-radius: var(--border-radius);
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: var(--transition-base);
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.btn-success {
    background: var(--gradient-success);
    color: white;
}

.btn-danger {
    background: var(--gradient-danger);
    color: white;
}

.btn-block {
    width: 100%;
}

/* ===== CARD STYLES ===== */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition-base);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
}

/* ===== MOBILE STYLES ===== */
@media (max-width: 768px) {
    /* Mobile App-like Navigation */
    .mobile-nav {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 1px solid #ddd;
        padding: 0.5rem 0;
        z-index: 1000;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-nav-item {
        text-align: center;
        padding: 0.5rem;
        color: var(--secondary-color);
        text-decoration: none;
        transition: var(--transition-base);
        display: block;
    }
    
    .mobile-nav-item.active,
    .mobile-nav-item:hover {
        color: var(--primary-color);
    }
    
    .mobile-nav-item i {
        font-size: 1.2rem;
        display: block;
        margin-bottom: 0.25rem;
    }
    
    .mobile-nav-item span {
        font-size: 0.75rem;
        display: block;
    }
    
    /* Adjust main content for mobile nav */
    .main-container {
        padding-bottom: 80px;
    }
    
    /* Mobile form adjustments */
    .auth-card {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    /* Mobile responsive text */
    h1 { font-size: 1.75rem; }
    h2 { font-size: 1.5rem; }
    h3 { font-size: 1.25rem; }
}

/* ===== DASHBOARD STYLES ===== */
.dashboard-header {
    background: var(--gradient-primary);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.dashboard-stats {
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    text-align: center;
    box-shadow: var(--box-shadow);
    transition: var(--transition-base);
    border: none;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* ===== VIP LEVEL STYLES ===== */
.vip-badge {
    background: var(--gradient-primary);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: bold;
    font-size: 0.9rem;
    display: inline-block;
}

.vip-progress {
    background: var(--light-color);
    border-radius: 1rem;
    overflow: hidden;
    height: 1rem;
    margin: 1rem 0;
}

.vip-progress-bar {
    background: var(--gradient-success);
    height: 100%;
    transition: width 0.6s ease;
}

/* ===== NOTIFICATION STYLES ===== */
.notification-banner {
    background: var(--gradient-success);
    color: white;
    padding: 1rem;
    text-align: center;
    margin-bottom: 1rem;
    border-radius: var(--border-radius);
}

.notification-banner .btn-close {
    filter: invert(1);
}

/* ===== POPUP STYLES ===== */
.welcome-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.popup-content {
    background: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    max-width: 400px;
    margin: 1rem;
    position: relative;
}

.popup-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--secondary-color);
}

/* ===== UTILITY CLASSES ===== */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient-primary {
    background: var(--gradient-primary);
}

.bg-gradient-success {
    background: var(--gradient-success);
}

.bg-gradient-danger {
    background: var(--gradient-danger);
}

.shadow-soft {
    box-shadow: var(--box-shadow);
}

.shadow-strong {
    box-shadow: var(--box-shadow-lg);
}

.rounded-custom {
    border-radius: var(--border-radius);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideInUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.animate-fade-in {
    animation: fadeIn var(--animation-duration) ease-out;
}

.animate-slide-up {
    animation: slideInUp var(--animation-duration) ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    .mobile-nav,
    .flash-messages,
    .loading-spinner {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}