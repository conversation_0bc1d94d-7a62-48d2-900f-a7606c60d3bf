<?php
/**
 * Bamboo Web Application - Standalone File Upload Test
 */

// Define app constant and base path for includes
define('BAMBOO_APP', true);
define('BASE_PATH', dirname(__DIR__));

// Include necessary files
require_once BASE_PATH . '/includes/config.php';
require_once BASE_PATH . '/includes/functions.php';

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_FILES['test_file']) && $_FILES['test_file']['error'] !== UPLOAD_ERR_NO_FILE) {
        
        // The target directory for the test
        $upload_dir = 'vip_icons/';
        
        echo "<p>Attempting to upload file to: <strong>" . UPLOAD_PATH . $upload_dir . "</strong></p>";

        // Call the function to be tested
        $result = handleFileUpload($_FILES['test_file'], $upload_dir);
        
        if ($result['success']) {
            $message = "<strong>SUCCESS!</strong> File '" . htmlspecialchars($result['filename']) . "' was uploaded successfully.";
            $message_type = 'success';
        } else {
            $message = "<strong>FAILURE!</strong> The upload failed. Reason: " . htmlspecialchars($result['message']);
            $message_type = 'danger';
        }
    } else {
        $message = "Please select a file to upload.";
        $message_type = 'warning';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="card">
            <div class="card-header">
                <h2>Standalone File Upload Test</h2>
            </div>
            <div class="card-body">
                <p>This page tests the core <code>handleFileUpload</code> function. Select a small image (PNG, JPG, SVG) and click upload.</p>
                
                <form action="" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="test_file" class="form-label">Select Icon File</label>
                        <input class="form-control" type="file" id="test_file" name="test_file" required>
                    </div>
                    <button type="submit" class="btn btn-primary">Upload Test File</button>
                </form>
                
                <?php if ($message): ?>
                    <div class="alert alert-<?php echo $message_type; ?> mt-4">
                        <?php echo $message; ?>
                    </div>
                <?php endif; ?>
            </div>
            <div class="card-footer">
                <p><strong>Target Directory:</strong> <code>/uploads/vip_icons/</code></p>
                <p><strong>Absolute Path Check:</strong> The script will try to write to <code><?php echo realpath(BASE_PATH . '/uploads/vip_icons/'); ?></code></p>
            </div>
        </div>
    </div>
</body>
</html>
