<?php
require_once 'c:\MAMP\htdocs\Bamboo\includes\database.php';
require_once 'c:\MAMP\htdocs\Bamboo\includes\config.php';

$pdo = getDB();

$queries = [
    "ALTER TABLE `users` ADD `usdt_wallet_address` VARCHAR(255) NULL DEFAULT NULL AFTER `avatar_url`;",
    "ALTER TABLE `users` ADD `credit_score` INT NOT NULL DEFAULT 100 AFTER `referral_count`;"
];

foreach ($queries as $query) {
    try {
        $pdo->exec($query);
        echo "Successfully executed: $query\n";
    } catch (PDOException $e) {
        echo "Error executing: $query\n";
        echo $e->getMessage() . "\n";
    }
}

