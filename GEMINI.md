we always analyze codebase to be sure of the code and structure , 
we use mcp mysql to check database 
create test to test complex fail then if the test work we use the method to fix the real code 
C:\MAMP\bin\mysql\bin\mysql -u root -proot matchmaking -e "SHOW TABLES;"

C:\MAMP\bin\mysql\bin\mysql -u root -proot matchmaking -e "DESCRIBE users;"



{
  "mcpServers": {
    "playwright-mcp": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@microsoft/playwright-mcp",
        "--key",
        "f93750d7-68e5-4a5a-a173-a9c3c7b03c11"
      ]
    }
  }
}

"desktop-commander": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@smithery/cli@latest",
        "run",
        "@wonderwhy-er/desktop-commander",
        "--key",
        "f93750d7-68e5-4a5a-a173-a9c3c7b03c11"
      ]
    }