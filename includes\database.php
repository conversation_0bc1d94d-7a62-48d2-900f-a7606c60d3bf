<?php
/**
 * Bamboo Web Application - Database Connection and Functions
 * Company: Notepadsly
 * Version: 1.0
 */

// Prevent direct access
if (!defined('BAMBOO_APP')) {
    die('Direct access not permitted');
}

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";port=" . DB_PORT . ";dbname=" . DB_NAME . ";charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            if (DEBUG_MODE) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed. Please try again later.");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// Database helper functions
function getDB() {
    return Database::getInstance()->getConnection();
}

/**
 * Execute a prepared statement with parameters
 */
function executeQuery($sql, $params = []) {
    try {
        $db = getDB();
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("Query execution failed: " . $e->getMessage() . " SQL: " . $sql . " File: " . $e->getFile() . " Line: " . $e->getLine());
        // Always log the exception details for debugging purposes
        return false;
    }
}

/**
 * Fetch a single row
 */
function fetchRow($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : false;
}

/**
 * Fetch all rows
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : false;
}

/**
 * Insert a record and return the last insert ID
 */
function insertRecord($table, $data) {
    $fields = array_keys($data);
    $placeholders = ':' . implode(', :', $fields);
    $sql = "INSERT INTO {$table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";
    
    $stmt = executeQuery($sql, $data);
    if ($stmt) {
        return getDB()->lastInsertId();
    }
    return false;
}

/**
 * Update a record
 */
function updateRecord($table, $data, $where, $whereParams = []) {
    $fields = array_keys($data);
    $setClause = implode(' = ?, ', $fields) . ' = ?';
    $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
    
    $params = array_merge(array_values($data), $whereParams);
    return executeQuery($sql, $params) !== false;
}

/**
 * Delete a record
 */
function deleteRecord($table, $where, $params = []) {
    $sql = "DELETE FROM {$table} WHERE {$where}";
    $stmt = executeQuery($sql, $params);
    if ($stmt === false) {
        logError("Failed to delete record from table '$table' with where clause '$where'. SQL: $sql", __FILE__, __LINE__);
        return false;
    }
    return true;
}

/**
 * Check if a record exists
 */
function recordExists($table, $where, $params = []) {
    $sql = "SELECT 1 FROM {$table} WHERE {$where} LIMIT 1";
    $result = fetchRow($sql, $params);
    return $result !== false;
}

/**
 * Get count of records
 */
function getRecordCount($table, $where = '1=1', $params = []) {
    $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
    $result = fetchRow($sql, $params);
    return $result ? (int)$result['count'] : 0;
}

/**
 * Begin transaction
 */
function beginTransaction() {
    return getDB()->beginTransaction();
}

/**
 * Commit transaction
 */
function commitTransaction() {
    return getDB()->commit();
}

/**
 * Rollback transaction
 */
function rollbackTransaction() {
    return getDB()->rollBack();
}

/**
 * Test database connection
 */
function testDatabaseConnection() {
    try {
        $db = getDB();
        $stmt = $db->query("SELECT 1");
        return $stmt !== false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * Check if a table exists in the database
 */
function tableExists($table) {
    try {
        $db = getDB();
        $stmt = $db->query("SHOW TABLES LIKE '{$table}'");
        return $stmt->rowCount() > 0;
    } catch (Exception $e) {
        return false;
    }
}
?>
