# Database Connection Test

## Quick Database Test

The `db_connection_test.php` file provides a simple and visual way to test your database connection.

### How to Use

1. **Open in Browser**: Navigate to `http://localhost/bamboo/test/db_connection_test.php`
2. **View Results**: The page will automatically test your database connection and display results
3. **Troubleshoot**: If there are issues, the page provides specific troubleshooting steps

### What It Tests

- ✅ **Configuration Check**: Verifies database settings are loaded
- ✅ **Connection Test**: Tests actual database connectivity  
- ✅ **Query Test**: Runs a simple query to verify functionality
- ✅ **Tables Check**: Lists all tables in your database
- ✅ **Data Check**: Shows sample data counts

### Features

- **Visual Interface**: Beautiful Semrush orange gradient background
- **Clear Results**: Color-coded success/error messages
- **Troubleshooting**: Specific steps to fix common issues
- **Quick Actions**: Links to other test files and main app

### Other Test Files

- `database_check.php` - Detailed database structure analysis
- `test_connection.php` - Full system connectivity test
- `quick_test.php` - Basic functionality test

### Troubleshooting Common Issues

1. **MAMP/XAMPP Not Running**: Start your local server and ensure MySQL is running
2. **Database Doesn't Exist**: Create the "matchmaking" database in phpMyAdmin
3. **Wrong Credentials**: Check username/password in `includes/config.php`
4. **Port Issues**: Verify MySQL is running on the correct port (usually 3306)
5. **Host Issues**: Try "127.0.0.1" instead of "localhost"

### Quick Setup

If you're setting up for the first time:

1. Start MAMP/XAMPP
2. Open phpMyAdmin: `http://localhost/phpMyAdmin`
3. Create database: `matchmaking`
4. Import: `database_migration.sql`
5. Test: `http://localhost/bamboo/test/db_connection_test.php`