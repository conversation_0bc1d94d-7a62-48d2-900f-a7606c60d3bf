<?php
/**
 * Bamboo Web Application - Database Connection Test
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include configuration
require_once 'C:\MAMP\htdocs\Bamboo\includes\config.php';
require_once '../includes/functions.php';

// Set content type
header('Content-Type: text/html; charset=UTF-8');

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bamboo - Connection Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #ff6900 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 1rem;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 1rem;
        }
        .status-success {
            color: #28a745;
        }
        .status-error {
            color: #dc3545;
        }
        .status-warning {
            color: #ffc107;
        }
        .test-item {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-left: 4px solid #dee2e6;
        }
        .test-item.success {
            background-color: #d4edda;
            border-left-color: #28a745;
        }
        .test-item.error {
            background-color: #f8d7da;
            border-left-color: #dc3545;
        }
        .test-item.warning {
            background-color: #fff3cd;
            border-left-color: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container test-container">
        <div class="test-card">
            <h1 class="text-center mb-4">
                <i class="bi bi-gear-fill" style="color: #ff6900;"></i>
                Bamboo System Test
            </h1>
            
            <div class="row">
                <div class="col-md-6">
                    <h3><i class="bi bi-info-circle"></i> System Information</h3>
                    
                    <div class="test-item">
                        <strong>PHP Version:</strong> <?php echo PHP_VERSION; ?>
                        <?php if (version_compare(PHP_VERSION, '8.0.0', '>=')): ?>
                            <i class="bi bi-check-circle status-success"></i>
                        <?php else: ?>
                            <i class="bi bi-exclamation-triangle status-warning"></i>
                        <?php endif; ?>
                    </div>
                    
                    <div class="test-item">
                        <strong>Server:</strong> <?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'; ?>
                    </div>
                    
                    <div class="test-item">
                        <strong>Base URL:</strong> <?php echo BASE_URL; ?>
                    </div>
                    
                    <div class="test-item">
                        <strong>Assets URL:</strong> <?php echo ASSETS_URL; ?>
                    </div>
                    
                    <div class="test-item">
                        <strong>Debug Mode:</strong> 
                        <?php echo DEBUG_MODE ? 'Enabled' : 'Disabled'; ?>
                        <?php if (DEBUG_MODE): ?>
                            <i class="bi bi-exclamation-triangle status-warning"></i>
                        <?php else: ?>
                            <i class="bi bi-check-circle status-success"></i>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h3><i class="bi bi-database"></i> Database Connection</h3>
                    
                    <?php
                    $db_status = 'error';
                    $db_message = 'Connection failed';
                    $db_details = [];
                    
                    try {
                        $db = getDB();
                        $db_status = 'success';
                        $db_message = 'Connection successful';
                        
                        // Test query
                        $stmt = $db->query("SELECT VERSION() as version");
                        $result = $stmt->fetch();
                        $db_details['MySQL Version'] = $result['version'] ?? 'Unknown';
                        
                        // Check database exists
                        $stmt = $db->query("SELECT DATABASE() as db_name");
                        $result = $stmt->fetch();
                        $db_details['Database'] = $result['db_name'] ?? 'None selected';
                        
                        // Check tables
                        $stmt = $db->query("SHOW TABLES");
                        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                        $db_details['Tables'] = count($tables) . ' tables found';
                        
                    } catch (Exception $e) {
                        $db_status = 'error';
                        $db_message = 'Error: ' . $e->getMessage();
                    }
                    ?>
                    
                    <div class="test-item <?php echo $db_status; ?>">
                        <strong>Status:</strong> <?php echo $db_message; ?>
                        <?php if ($db_status === 'success'): ?>
                            <i class="bi bi-check-circle status-success"></i>
                        <?php else: ?>
                            <i class="bi bi-x-circle status-error"></i>
                        <?php endif; ?>
                    </div>
                    
                    <?php if (!empty($db_details)): ?>
                        <?php foreach ($db_details as $key => $value): ?>
                            <div class="test-item">
                                <strong><?php echo $key; ?>:</strong> <?php echo $value; ?>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                    
                    <div class="test-item">
                        <strong>Host:</strong> <?php echo DB_HOST; ?>:<?php echo DB_PORT; ?>
                    </div>
                    
                    <div class="test-item">
                        <strong>Database:</strong> <?php echo DB_NAME; ?>
                    </div>
                </div>
            </div>
            
            <hr>
            
            <h3><i class="bi bi-folder"></i> Directory Structure</h3>
            <div class="row">
                <div class="col-md-6">
                    <?php
                    $directories = [
                        'includes' => 'includes/',
                        'assets' => 'assets/',
                        'user' => 'user/',
                        'admin' => 'admin/',
                        'uploads' => 'uploads/',
                        'logs' => 'logs/'
                    ];
                    
                    foreach ($directories as $name => $path) {
                        $full_path = dirname(__DIR__) . '/' . $path;
                        $exists = is_dir($full_path);
                        $writable = $exists ? is_writable($full_path) : false;
                        
                        echo '<div class="test-item ' . ($exists ? 'success' : 'error') . '">';
                        echo '<strong>' . ucfirst($name) . ':</strong> ';
                        echo $exists ? 'Exists' : 'Missing';
                        
                        if ($exists) {
                            echo ' (' . ($writable ? 'Writable' : 'Read-only') . ')';
                            echo '<i class="bi bi-check-circle status-success"></i>';
                        } else {
                            echo '<i class="bi bi-x-circle status-error"></i>';
                        }
                        echo '</div>';
                    }
                    ?>
                </div>
                
                <div class="col-md-6">
                    <h4>File Permissions</h4>
                    <?php
                    $files = [
                        '.htaccess' => '.htaccess',
                        'index.php' => 'index.php',
                        'config.php' => 'includes/config.php',
                        'database.php' => 'includes/database.php'
                    ];
                    
                    foreach ($files as $name => $path) {
                        $full_path = dirname(__DIR__) . '/' . $path;
                        $exists = file_exists($full_path);
                        $readable = $exists ? is_readable($full_path) : false;
                        
                        echo '<div class="test-item ' . ($exists && $readable ? 'success' : 'error') . '">';
                        echo '<strong>' . $name . ':</strong> ';
                        echo $exists ? ($readable ? 'OK' : 'Not readable') : 'Missing';
                        
                        if ($exists && $readable) {
                            echo '<i class="bi bi-check-circle status-success"></i>';
                        } else {
                            echo '<i class="bi bi-x-circle status-error"></i>';
                        }
                        echo '</div>';
                    }
                    ?>
                </div>
            </div>
            
            <hr>
            
            <h3><i class="bi bi-link"></i> URL Testing</h3>
            <div class="row">
                <div class="col-12">
                    <div class="test-item">
                        <strong>Current URL:</strong> <?php echo getCurrentUrl(); ?>
                    </div>
                    
                    <div class="test-item">
                        <strong>Test Links:</strong><br>
                        <a href="<?php echo BASE_URL; ?>" class="btn btn-sm btn-outline-primary me-2">Home</a>
                        <a href="<?php echo BASE_URL; ?>user/login/" class="btn btn-sm btn-outline-primary me-2">Login</a>
                        <a href="<?php echo BASE_URL; ?>user/register/" class="btn btn-sm btn-outline-primary me-2">Register</a>
                        <a href="<?php echo BASE_URL; ?>admin/" class="btn btn-sm btn-outline-primary">Admin</a>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <button onclick="window.location.reload()" class="btn btn-primary">
                    <i class="bi bi-arrow-clockwise"></i> Refresh Test
                </button>
                <a href="<?php echo BASE_URL; ?>" class="btn btn-outline-primary">
                    <i class="bi bi-house"></i> Go to Application
                </a>
            </div>
        </div>
        
        <div class="text-center">
            <small class="text-white">
                <?php echo COMPANY_NAME; ?> &copy; <?php echo date('Y'); ?> - 
                <?php echo APP_NAME; ?> v<?php echo APP_VERSION; ?>
            </small>
        </div>
    </div>
</body>
</html>