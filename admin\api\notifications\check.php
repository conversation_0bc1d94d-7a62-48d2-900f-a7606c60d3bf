<?php
/**
 * Bamboo Web Application - Notifications Check API
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access',
        'count' => 0
    ]);
    exit;
}

// For now, return empty notifications
// In the future, implement actual notification checking logic
header('Content-Type: application/json');
echo json_encode([
    'success' => true,
    'count' => 0,
    'notifications' => [],
    'timestamp' => date('Y-m-d H:i:s')
]);
exit;
?>