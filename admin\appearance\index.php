<?php
/**
 * Bamboo Web Application - Admin Appearance Settings
 * Company: Notepadsly
 * Version: 1.0
 */

// Define app constant
define('BAMBOO_APP', true);

// Include required files
require_once '../../includes/config.php';
require_once '../../includes/functions.php';

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    showError('You must be logged in to access the admin dashboard.');
    redirect('admin/login/');
    exit;
}

// Function to handle favicon upload
function handleFaviconUpload($file) {
    $upload_dir = '../../assets/images/';
    $allowed_types = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/jpeg', 'image/jpg'];
    $max_size = 2 * 1024 * 1024; // 2MB

    // Validate file type
    $file_type = $file['type'];
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

    // Allow ICO files even if MIME type is not detected correctly
    if ($file_extension === 'ico') {
        $file_type = 'image/x-icon';
    }

    if (!in_array($file_type, $allowed_types) && !in_array($file_extension, ['ico', 'png', 'jpg', 'jpeg'])) {
        throw new Exception('Invalid file type. Please upload ICO, PNG, or JPG files only.');
    }

    // Validate file size
    if ($file['size'] > $max_size) {
        throw new Exception('File size too large. Maximum size is 2MB.');
    }

    // Create upload directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }

    // Backup current favicon if it exists
    $current_favicon = $upload_dir . 'favicon.ico';
    if (file_exists($current_favicon)) {
        $backup_name = $upload_dir . 'favicon_backup_' . date('Y-m-d_H-i-s') . '.ico';
        copy($current_favicon, $backup_name);
    }

    // Determine target filename
    $target_file = $upload_dir . 'favicon.ico';

    // For non-ICO files, we'll keep them as favicon.ico for compatibility
    if (move_uploaded_file($file['tmp_name'], $target_file)) {
        // Log the favicon update
        error_log("Favicon updated successfully by admin: " . ($_SESSION['admin_username'] ?? 'unknown'));
        return true;
    } else {
        throw new Exception('Failed to upload favicon. Please try again.');
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Handle favicon upload first
        $favicon_uploaded = false;
        if (isset($_FILES['favicon_upload']) && $_FILES['favicon_upload']['error'] === UPLOAD_ERR_OK) {
            $favicon_uploaded = handleFaviconUpload($_FILES['favicon_upload']);
        }

        // Save appearance settings
        $settings = [
            'primary_color' => $_POST['primary_color'] ?? '#ff6900',
            'secondary_color' => $_POST['secondary_color'] ?? '#ffffff',
            'accent_color' => $_POST['accent_color'] ?? '#007bff',
            'gradient_start' => $_POST['gradient_start'] ?? '#ff6900',
            'gradient_end' => $_POST['gradient_end'] ?? '#ff8533',
            'card_background' => $_POST['card_background'] ?? '#ffffff',
            'sidebar_style' => $_POST['sidebar_style'] ?? 'gradient',
            'sidebar_text_color' => $_POST['sidebar_text_color'] ?? '#ffffff',
            'card_shadow' => $_POST['card_shadow'] ?? 'subtle',
            'border_radius' => $_POST['border_radius'] ?? '0.5rem',
            'button_style' => $_POST['button_style'] ?? 'gradient',
            'logo_size' => $_POST['logo_size'] ?? 'medium',
            'table_header_color' => $_POST['table_header_color'] ?? 'primary',
            'table_header_text_color' => $_POST['table_header_text_color'] ?? '#ffffff',
            'level_badge_color' => $_POST['level_badge_color'] ?? 'primary',
            'footer_bg_color' => $_POST['footer_bg_color'] ?? '#f8f9fa',
            'footer_text_color' => $_POST['footer_text_color'] ?? '#6c757d',
            'theme_mode' => $_POST['theme_mode'] ?? 'light'
        ];
        
        foreach ($settings as $key => $value) {
            updateSetting('appearance_' . $key, $value);
        }
        
        $success_message = 'Appearance settings updated successfully!';
        if ($favicon_uploaded) {
            $success_message .= ' Favicon has been updated.';
        }
        showSuccess($success_message);
        redirect('admin/appearance/');
        
    } catch (Exception $e) {
        showError('Failed to update appearance settings: ' . $e->getMessage());
    }
}

// Get current appearance settings
$current_settings = [
    'primary_color' => getAppSetting('appearance_primary_color', '#ff6900'),
    'secondary_color' => getAppSetting('appearance_secondary_color', '#ffffff'),
    'accent_color' => getAppSetting('appearance_accent_color', '#007bff'),
    'gradient_start' => getAppSetting('appearance_gradient_start', '#ff6900'),
    'gradient_end' => getAppSetting('appearance_gradient_end', '#ff8533'),
    'card_background' => getAppSetting('appearance_card_background', '#ffffff'),
    'sidebar_style' => getAppSetting('appearance_sidebar_style', 'gradient'),
    'sidebar_text_color' => getAppSetting('appearance_sidebar_text_color', '#ffffff'),
    'card_shadow' => getAppSetting('appearance_card_shadow', 'subtle'),
    'border_radius' => getAppSetting('appearance_border_radius', '0.5rem'),
    'button_style' => getAppSetting('appearance_button_style', 'gradient'),
    'theme_mode' => getAppSetting('appearance_theme_mode', 'light'),
    'logo_size' => getAppSetting('appearance_logo_size', 'medium'),
    'table_header_color' => getAppSetting('appearance_table_header_color', 'primary'),
    'table_header_text_color' => getAppSetting('appearance_table_header_text_color', '#ffffff'),
    'level_badge_color' => getAppSetting('appearance_level_badge_color', 'primary'),
    'footer_bg_color' => getAppSetting('appearance_footer_bg_color', '#f8f9fa'),
    'footer_text_color' => getAppSetting('appearance_footer_text_color', '#6c757d')
];

// Page configuration
$page_title = 'Appearance Settings';
$body_class = 'admin-page';
$additional_css = [
    BASE_URL . 'admin/assets/css/admin.css',
    BASE_URL . 'admin/appearance/appearance.css'
];

// Include admin header
include '../includes/admin_header.php';
?>

<div class="admin-wrapper">
    <!-- Sidebar -->
    <?php include '../includes/admin_sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="admin-main">
        <!-- Top Header -->
        <?php include '../includes/admin_topbar.php'; ?>
        
        <!-- Content Area -->
        <div class="admin-content">
            <div class="container-fluid">
                
                <!-- Page Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1 class="h3 mb-0">Appearance Settings</h1>
                        <p class="text-muted">Customize the look and feel of your admin dashboard</p>
                    </div>
                    <div>
                        <button type="button" class="btn btn-outline-secondary" id="resetToDefault">
                            <i class="bi bi-arrow-clockwise me-2"></i>Reset to Default
                        </button>
                    </div>
                </div>
                
                <form method="POST" id="appearanceForm" enctype="multipart/form-data">

                    <!-- BASIC COLORS SECTION -->
                    <div class="appearance-section mb-5">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-palette me-2"></i>Basic Colors
                            </h4>
                            <p class="section-description">Configure the main color scheme for your admin interface</p>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="primary_color" class="form-label fw-semibold">Primary Color</label>
                                        <p class="text-muted small mb-2">Main brand color used throughout the interface</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="primary_color" name="primary_color"
                                                   value="<?php echo htmlspecialchars($current_settings['primary_color']); ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo htmlspecialchars($current_settings['primary_color']); ?>"
                                                   data-color-input="primary_color">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="secondary_color" class="form-label fw-semibold">Secondary Color</label>
                                        <p class="text-muted small mb-2">Supporting color for secondary elements</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="secondary_color" name="secondary_color"
                                                   value="<?php echo htmlspecialchars($current_settings['secondary_color']); ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo htmlspecialchars($current_settings['secondary_color']); ?>"
                                                   data-color-input="secondary_color">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="accent_color" class="form-label fw-semibold">Accent Color</label>
                                        <p class="text-muted small mb-2">Highlight color for buttons and accents</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="accent_color" name="accent_color"
                                                   value="<?php echo htmlspecialchars($current_settings['accent_color']); ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo htmlspecialchars($current_settings['accent_color']); ?>"
                                                   data-color-input="accent_color">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="card_background" class="form-label fw-semibold">Card Background</label>
                                        <p class="text-muted small mb-2">Background color for cards and panels</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="card_background" name="card_background"
                                                   value="<?php echo htmlspecialchars($current_settings['card_background']); ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo htmlspecialchars($current_settings['card_background']); ?>"
                                                   data-color-input="card_background">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- GRADIENT SETTINGS SECTION -->
                    <div class="appearance-section mb-5">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-rainbow me-2"></i>Gradient Settings
                            </h4>
                            <p class="section-description">Configure gradient colors for backgrounds and effects</p>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="gradient_start" class="form-label fw-semibold">Gradient Start Color</label>
                                        <p class="text-muted small mb-2">Starting color for gradient effects</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="gradient_start" name="gradient_start"
                                                   value="<?php echo htmlspecialchars($current_settings['gradient_start']); ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo htmlspecialchars($current_settings['gradient_start']); ?>"
                                                   data-color-input="gradient_start">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="gradient_end" class="form-label fw-semibold">Gradient End Color</label>
                                        <p class="text-muted small mb-2">Ending color for gradient effects</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="gradient_end" name="gradient_end"
                                                   value="<?php echo htmlspecialchars($current_settings['gradient_end']); ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo htmlspecialchars($current_settings['gradient_end']); ?>"
                                                   data-color-input="gradient_end">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label class="form-label fw-semibold">Gradient Preview</label>
                                        <p class="text-muted small mb-2">Live preview of your gradient</p>
                                        <div class="gradient-preview" id="gradientPreview">
                                            <span>Gradient Preview</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SIDEBAR SETTINGS SECTION -->
                    <div class="appearance-section mb-5">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-layout-sidebar me-2"></i>Sidebar Settings
                            </h4>
                            <p class="section-description">Customize the appearance of the admin sidebar</p>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="sidebar_style" class="form-label fw-semibold">Sidebar Style</label>
                                        <p class="text-muted small mb-2">Choose the visual style for the sidebar</p>
                                        <select class="form-select" id="sidebar_style" name="sidebar_style">
                                            <option value="gradient" <?php echo $current_settings['sidebar_style'] === 'gradient' ? 'selected' : ''; ?>>Gradient</option>
                                            <option value="solid" <?php echo $current_settings['sidebar_style'] === 'solid' ? 'selected' : ''; ?>>Solid Color</option>
                                            <option value="light" <?php echo $current_settings['sidebar_style'] === 'light' ? 'selected' : ''; ?>>Light Theme</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="sidebar_text_color" class="form-label fw-semibold">Sidebar Text Color</label>
                                        <p class="text-muted small mb-2">Color for sidebar menu text and icons</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="sidebar_text_color" name="sidebar_text_color"
                                                   value="<?php echo $current_settings['sidebar_text_color'] ?? '#ffffff'; ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo strtoupper($current_settings['sidebar_text_color'] ?? '#FFFFFF'); ?>"
                                                   data-color-input="sidebar_text_color">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="logo_size" class="form-label fw-semibold">Logo Size</label>
                                        <p class="text-muted small mb-2">Size of the logo in the sidebar</p>
                                        <select class="form-select" id="logo_size" name="logo_size">
                                            <option value="small" <?php echo $current_settings['logo_size'] === 'small' ? 'selected' : ''; ?>>Small (60px)</option>
                                            <option value="medium" <?php echo $current_settings['logo_size'] === 'medium' ? 'selected' : ''; ?>>Medium (80px)</option>
                                            <option value="large" <?php echo $current_settings['logo_size'] === 'large' ? 'selected' : ''; ?>>Large (120px)</option>
                                            <option value="extra-large" <?php echo $current_settings['logo_size'] === 'extra-large' ? 'selected' : ''; ?>>Extra Large (150px)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- LAYOUT & STYLING SECTION -->
                    <div class="appearance-section mb-5">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-brush me-2"></i>Layout & Styling
                            </h4>
                            <p class="section-description">Configure visual elements and layout preferences</p>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="theme_mode" class="form-label fw-semibold">Theme Mode</label>
                                        <p class="text-muted small mb-2">Overall theme appearance</p>
                                        <select class="form-select" id="theme_mode" name="theme_mode">
                                            <option value="light" <?php echo $current_settings['theme_mode'] === 'light' ? 'selected' : ''; ?>>Light</option>
                                            <option value="dark" <?php echo $current_settings['theme_mode'] === 'dark' ? 'selected' : ''; ?>>Dark</option>
                                            <option value="auto" <?php echo $current_settings['theme_mode'] === 'auto' ? 'selected' : ''; ?>>Auto</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="card_shadow" class="form-label fw-semibold">Card Shadow</label>
                                        <p class="text-muted small mb-2">Shadow depth for cards and panels</p>
                                        <select class="form-select" id="card_shadow" name="card_shadow">
                                            <option value="none" <?php echo $current_settings['card_shadow'] === 'none' ? 'selected' : ''; ?>>No Shadow</option>
                                            <option value="subtle" <?php echo $current_settings['card_shadow'] === 'subtle' ? 'selected' : ''; ?>>Subtle</option>
                                            <option value="medium" <?php echo $current_settings['card_shadow'] === 'medium' ? 'selected' : ''; ?>>Medium</option>
                                            <option value="strong" <?php echo $current_settings['card_shadow'] === 'strong' ? 'selected' : ''; ?>>Strong</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="border_radius" class="form-label fw-semibold">Border Radius</label>
                                        <p class="text-muted small mb-2">Roundness of corners for elements</p>
                                        <select class="form-select" id="border_radius" name="border_radius">
                                            <option value="0" <?php echo $current_settings['border_radius'] === '0' ? 'selected' : ''; ?>>Sharp (0px)</option>
                                            <option value="0.25rem" <?php echo $current_settings['border_radius'] === '0.25rem' ? 'selected' : ''; ?>>Small (4px)</option>
                                            <option value="0.5rem" <?php echo $current_settings['border_radius'] === '0.5rem' ? 'selected' : ''; ?>>Medium (8px)</option>
                                            <option value="0.75rem" <?php echo $current_settings['border_radius'] === '0.75rem' ? 'selected' : ''; ?>>Large (12px)</option>
                                            <option value="1rem" <?php echo $current_settings['border_radius'] === '1rem' ? 'selected' : ''; ?>>Extra Large (16px)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="button_style" class="form-label fw-semibold">Button Style</label>
                                        <p class="text-muted small mb-2">Default style for action buttons</p>
                                        <select class="form-select" id="button_style" name="button_style">
                                            <option value="gradient" <?php echo ($current_settings['button_style'] ?? 'gradient') === 'gradient' ? 'selected' : ''; ?>>Gradient (Primary + Accent)</option>
                                            <option value="primary" <?php echo ($current_settings['button_style'] ?? 'gradient') === 'primary' ? 'selected' : ''; ?>>Primary Color Only</option>
                                            <option value="accent" <?php echo ($current_settings['button_style'] ?? 'gradient') === 'accent' ? 'selected' : ''; ?>>Accent Color Only</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- TABLE & COMPONENT SETTINGS SECTION -->
                    <div class="appearance-section mb-5">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-table me-2"></i>Table & Component Settings
                            </h4>
                            <p class="section-description">Customize tables, badges, and other interface components</p>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="table_header_color" class="form-label fw-semibold">Table Header Background</label>
                                        <p class="text-muted small mb-2">Background color for table headers</p>
                                        <select class="form-select" id="table_header_color" name="table_header_color">
                                            <option value="primary" <?php echo $current_settings['table_header_color'] === 'primary' ? 'selected' : ''; ?>>Primary Color</option>
                                            <option value="accent" <?php echo $current_settings['table_header_color'] === 'accent' ? 'selected' : ''; ?>>Accent Color</option>
                                            <option value="secondary" <?php echo $current_settings['table_header_color'] === 'secondary' ? 'selected' : ''; ?>>Secondary Color</option>
                                            <option value="gradient" <?php echo $current_settings['table_header_color'] === 'gradient' ? 'selected' : ''; ?>>Primary Gradient</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="table_header_text_color" class="form-label fw-semibold">Table Header Text Color</label>
                                        <p class="text-muted small mb-2">Text color for table header content</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="table_header_text_color" name="table_header_text_color"
                                                   value="<?php echo $current_settings['table_header_text_color'] ?? '#ffffff'; ?>">
                                            <input type="text" class="form-control color-text"
                                                   value="<?php echo strtoupper($current_settings['table_header_text_color'] ?? '#FFFFFF'); ?>"
                                                   data-color-input="table_header_text_color">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="level_badge_color" class="form-label fw-semibold">Membership Badge Color</label>
                                        <p class="text-muted small mb-2">Color scheme for membership level badges</p>
                                        <select class="form-select" id="level_badge_color" name="level_badge_color">
                                            <option value="primary" <?php echo $current_settings['level_badge_color'] === 'primary' ? 'selected' : ''; ?>>Primary Color</option>
                                            <option value="accent" <?php echo $current_settings['level_badge_color'] === 'accent' ? 'selected' : ''; ?>>Accent Color</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- WEBSITE BRANDING SECTION -->
                    <div class="appearance-section mb-5">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-globe me-2"></i>Website Branding
                            </h4>
                            <p class="section-description">Configure website branding elements like favicon</p>
                        </div>

                        <div class="row">
                            <div class="col-lg-6 col-md-8 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="favicon_upload" class="form-label fw-semibold">Website Favicon</label>
                                        <p class="text-muted small mb-3">Upload a favicon for your website (.ico, .png, .jpg - max 2MB)</p>

                                        <!-- Current Favicon Display -->
                                        <div class="current-favicon mb-3">
                                            <label class="form-label small">Current Favicon:</label>
                                            <div class="favicon-preview d-flex align-items-center gap-2">
                                                <img id="current-favicon-preview"
                                                     src="<?php echo BASE_URL; ?>assets/images/favicon.ico?v=<?php echo time(); ?>"
                                                     alt="Current Favicon"
                                                     style="width: 32px; height: 32px; border: 1px solid #ddd; border-radius: 4px;"
                                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                                                <span class="text-muted small" style="display: none;">No favicon set</span>
                                            </div>
                                        </div>

                                        <!-- File Upload -->
                                        <input type="file" class="form-control" id="favicon_upload" name="favicon_upload"
                                               accept=".ico,.png,.jpg,.jpeg" onchange="previewFavicon(this)">
                                        <div class="form-text">Recommended: 32x32px ICO file for best compatibility</div>

                                        <!-- Preview -->
                                        <div id="favicon-preview-container" class="mt-3" style="display: none;">
                                            <label class="form-label small">Preview:</label>
                                            <div class="d-flex align-items-center gap-2">
                                                <img id="favicon-preview" style="width: 32px; height: 32px; border: 1px solid #ddd; border-radius: 4px;">
                                                <span class="text-success small">Ready to upload</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- FOOTER SETTINGS SECTION -->
                    <div class="appearance-section mb-5">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-layout-text-window-reverse me-2"></i>Footer Settings
                            </h4>
                            <p class="section-description">Customize the website footer appearance</p>
                        </div>

                        <div class="row">
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="footer_bg_color" class="form-label fw-semibold">Footer Background</label>
                                        <p class="text-muted small mb-2">Background color for the footer area</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="footer_bg_color" name="footer_bg_color"
                                                   value="<?php echo $current_settings['footer_bg_color']; ?>">
                                            <input type="text" class="form-control color-text"
                                                   data-color-input="footer_bg_color"
                                                   value="<?php echo strtoupper($current_settings['footer_bg_color']); ?>"
                                                   placeholder="#F8F9FA">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card setting-card">
                                    <div class="card-body">
                                        <label for="footer_text_color" class="form-label fw-semibold">Footer Text Color</label>
                                        <p class="text-muted small mb-2">Text color for footer content</p>
                                        <div class="color-input-group">
                                            <input type="color" class="form-control form-control-color"
                                                   id="footer_text_color" name="footer_text_color"
                                                   value="<?php echo $current_settings['footer_text_color']; ?>">
                                            <input type="text" class="form-control color-text"
                                                   data-color-input="footer_text_color"
                                                   value="<?php echo strtoupper($current_settings['footer_text_color']); ?>"
                                                   placeholder="#6C757D">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- LIVE PREVIEW SECTION -->
                    <div class="appearance-section mb-4">
                        <div class="section-header mb-4">
                            <h4 class="section-title">
                                <i class="bi bi-eye me-2"></i>Live Preview
                            </h4>
                            <p class="section-description">See how your changes will look in real-time</p>
                        </div>

                        <div class="preview-container">
                            <!-- Mini Dashboard Preview -->
                            <div class="mini-dashboard" id="previewDashboard">
                                <div class="mini-sidebar" id="previewSidebar">
                                    <div class="mini-brand">Dashboard</div>
                                    <div class="mini-nav">
                                        <div class="mini-nav-item active">Home</div>
                                        <div class="mini-nav-item">Settings</div>
                                        <div class="mini-nav-item">Users</div>
                                    </div>
                                </div>
                                <div class="mini-content">
                                    <div class="mini-card" id="previewCard1">
                                        <div class="mini-card-header">Statistics</div>
                                        <div class="mini-card-body">Sample content</div>
                                    </div>
                                    <div class="mini-card" id="previewCard2">
                                        <div class="mini-card-header">Sample Table</div>
                                        <div class="mini-card-body">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Status</th>
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>John Doe</td>
                                                        <td><span class="badge bg-success">Active</span></td>
                                                        <td><button class="btn btn-sm btn-primary">Edit</button></td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Save Button Section -->
                    <div class="appearance-section mb-3">
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg me-3">
                                <i class="bi bi-check-circle me-2"></i>Save Appearance Settings
                            </button>
                            <button type="button" class="btn btn-outline-secondary btn-lg" id="previewChanges">
                                <i class="bi bi-eye me-2"></i>Preview Changes
                            </button>
                        </div>
                    </div>
                </form>
                
            </div>
        </div>
        
        <!-- Footer -->
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>

<?php 
$additional_js = [
    BASE_URL . 'admin/assets/js/admin.js',
    BASE_URL . 'admin/appearance/appearance.js'
];
include '../includes/admin_footer_scripts.php';
?>
