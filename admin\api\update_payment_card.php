<?php
/**
 * Bamboo Web Application - Update Payment Card API
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../../../includes/config.php';
require_once '../../../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

header('Content-Type: application/json');

// Check if admin is logged in
if (!isAdminLoggedIn()) {
    jsonResponse(['success' => false, 'message' => 'Unauthorized access.'], 401);
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['success' => false, 'message' => 'Invalid request method.'], 405);
}

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    jsonResponse(['success' => false, 'message' => 'Invalid security token. Please try again.'], 403);
}

$user_id = (int)($_POST['user_id'] ?? 0);
$usdt_wallet_address = sanitizeInput($_POST['usdt_wallet_address'] ?? '');
$exchange_name = sanitizeInput($_POST['exchange_name'] ?? '');

if ($user_id <= 0) {
    jsonResponse(['success' => false, 'message' => 'Invalid user ID.'], 400);
}

try {
    $data = [
        'usdt_wallet_address' => $usdt_wallet_address,
        'exchange_name' => $exchange_name
    ];

    $update_success = updateRecord('users', $data, 'id = ?', [$user_id]);

    if ($update_success) {
        jsonResponse(['success' => true, 'message' => 'Payment card information updated successfully!']);
    } else {
        jsonResponse(['success' => false, 'message' => 'Failed to update payment card information.'], 500);
    }

} catch (Exception $e) {
    logError('Error updating payment card info: ' . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'An unexpected error occurred.'], 500);
}

?>