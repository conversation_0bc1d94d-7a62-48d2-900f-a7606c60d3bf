<?php
/**
 * Bamboo Web Application - Edit Admin Profile
 * Company: Notepadsly
 * Version: 1.0
 */

define('BAMBOO_APP', true);
require_once '../includes/config.php';
require_once '../includes/functions.php';

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isAdminLoggedIn()) {
    redirect('../login/');
}

$admin_id = $_SESSION['admin_id'] ?? 0;
if (!$admin_id) {
    showError('Invalid admin session.');
    redirect('../login/');
}

// Fetch admin info
$admin = fetchRow("SELECT * FROM admins WHERE id = ?", [$admin_id]);
if (!$admin) {
    showError('Admin not found.');
    redirect('../login/');
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $full_name = trim($_POST['full_name'] ?? '');
    $email = trim($_POST['email'] ?? '');
    if (!$full_name || !$email) {
        showError('Full name and email are required.');
    } else {
        executeQuery("UPDATE admins SET full_name = ?, email = ? WHERE id = ?", [$full_name, $email, $admin_id]);
        showSuccess('Profile updated successfully.');
        redirect('index.php');
    }
}

$page_title = 'Edit Profile';
include '../includes/admin_header.php';
?>
<div class="admin-wrapper">
    <?php include '../includes/admin_sidebar.php'; ?>
    <div class="admin-main">
        <?php include '../includes/admin_topbar.php'; ?>
        <div class="admin-content">
            <div class="container py-5">
                <div class="card mx-auto" style="max-width: 500px;">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">Edit Profile</h4>
                    </div>
                    <div class="card-body">
                        <form method="post">
                            <div class="mb-3">
                                <label class="form-label">Full Name</label>
                                <input type="text" class="form-control" name="full_name" value="<?php echo htmlspecialchars($admin['full_name']); ?>" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email</label>
                                <input type="email" class="form-control" name="email" value="<?php echo htmlspecialchars($admin['email']); ?>" required>
                            </div>
                            <div class="d-flex justify-content-between">
                                <a href="index.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <?php include '../includes/admin_footer.php'; ?>
    </div>
</div>
<?php include '../includes/admin_footer_scripts.php'; ?>
